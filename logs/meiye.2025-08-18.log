2025-08-18 09:19:35.827 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-18 09:19:35.831 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 71396 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-18 09:19:35.831 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "dev"
2025-08-18 09:19:36.963 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-18 09:19:36.965 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-18 09:19:37.032 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-18 09:19:37.292 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=28091920-0671-3787-8121-5082b2e149ac
2025-08-18 09:19:37.536 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 09:19:37.538 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 09:19:37.540 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 09:19:37.762 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8100 (http)
2025-08-18 09:19:37.768 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8100"]
2025-08-18 09:19:37.768 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-18 09:19:37.768 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-18 09:19:37.866 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-18 09:19:37.866 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1961 ms
2025-08-18 09:19:38.597 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-18 09:19:38.617 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-08-18 09:19:39.080 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 09:19:39.188 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 09:19:39.697 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-18 09:19:39.981 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-18 09:19:40.489 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载华为云 OBS 存储平台：huawei-obs-1
2025-08-18 09:19:41.065 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-18 09:19:41.170 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-18 09:19:41.644 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-18 09:19:42.307 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8100"]
2025-08-18 09:19:42.314 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8100 (http) with context path '/api/meeting'
2025-08-18 09:19:42.317 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-18 09:19:42.321 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-18 09:19:42.340 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-18 09:19:42.432 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-08-18 09:19:42.547 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-08-18 09:19:42.553 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-08-18 09:19:42.554 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-08-18 09:19:42.566 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-08-18 09:19:42.579 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-08-18 09:19:42.581 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2025-08-18 09:19:42.583 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-08-18 09:19:42.619 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 09:19:42.628 [main] INFO  cn.july.orch.meeting.Application - Started Application in 7.148 seconds (JVM running for 8.154)
2025-08-18 09:19:42.753 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 09:19:42.754 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:19:42.754 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:19:42.882 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:19:42.882 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 开始处理超期任务
2025-08-18 09:19:42.907 [RMI TCP Connection(1)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 09:19:42.907 [RMI TCP Connection(1)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-18 09:19:42.909 [RMI TCP Connection(1)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-18 09:19:42.911 [scheduling-1] INFO  c.j.o.m.service.TaskActionService - 处理超期任务
2025-08-18 09:19:42.979 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 处理超期任务完成
2025-08-18 09:20:00.022 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 09:20:00.709 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 09:20:00.766 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 09:20:00.840 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 09:20:00.894 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 09:20:00.965 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 09:20:01.038 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 09:20:01.119 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 09:20:01.175 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 09:20:01.229 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 09:20:01.277 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 09:20:01.321 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 09:20:01.321 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 09:20:42.623 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:20:42.629 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:20:42.891 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:21:54.791 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:21:54.794 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:22:59.457 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m4s643ms).
2025-08-18 09:22:59.488 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x712c45b5, L:/***********:58554 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 09:22:59.500 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:22:59.501 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:22:59.501 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:22:59.609 [http-nio-8100-exec-1] WARN  c.j.o.m.c.MeetingPlanController - [logCost][createMeetingPlan][113123][127.0.0.1][/api/meeting/meeting-plan/create], args={}, requestBody={  "planName": "会议规划5",  "plannedStartTime": "2025-08-23 18:53:05",  "plannedEndTime": "2025-08-23 20:53:05",  "meetingLocation": "1",  "meetingStandardId": 1,  "priorityLevel": 1,  "attendees": [    "ou_ca11635b2ad48203b793d90d78f10a0e",    "ou_755ea7451f97828ca8038c179d0217e2",    "ou_b6673c6de16f2864289f8e06b02f4be7"  ],  "planDescription": "啊啊啊"}, result={"success":true,"traceid":"","code":"000000200","msg":"success","timestamp":1755480179588}
2025-08-18 09:22:59.627 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:23:43.281 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:24:52.020 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:24:52.025 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xae1763d2, L:/***********:58539 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 09:24:52.026 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x43a8e169, L:/***********:58541 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 09:24:52.026 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xefc543fb, L:/***********:58551 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 09:24:52.027 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xb42d2096, L:/***********:58784 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 09:24:52.164 [http-nio-8100-exec-3] WARN  c.j.o.m.c.MeetingPlanController - [logCost][createMeetingPlan][100655][127.0.0.1][/api/meeting/meeting-plan/create], args={}, requestBody={  "planName": "会议规划6",  "plannedStartTime": "2025-08-24 18:53:05",  "plannedEndTime": "2025-08-24 20:53:05",  "meetingLocation": "1",  "meetingStandardId": 1,  "priorityLevel": 1,  "attendees": [    "ou_ca11635b2ad48203b793d90d78f10a0e",    "ou_755ea7451f97828ca8038c179d0217e2",    "ou_b6673c6de16f2864289f8e06b02f4be7"  ],  "planDescription": "啊啊啊"}, result={"success":true,"traceid":"","code":"000000200","msg":"success","timestamp":1755480292161}
2025-08-18 09:24:52.210 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:24:52.210 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:24:52.210 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:24:52.381 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:25:22.024 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m12s662ms).
2025-08-18 09:25:42.603 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:25:42.607 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:25:42.767 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:26:42.605 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:26:42.608 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:26:42.748 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:27:42.607 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:27:42.611 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:27:42.754 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:28:42.604 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:28:42.607 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:28:42.788 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:29:42.603 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 09:29:42.670 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 09:29:42.670 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:29:42.670 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:29:42.809 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:29:59.987 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 09:30:00.524 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 09:30:00.575 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 09:30:00.631 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 09:30:00.696 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 09:30:00.779 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 09:30:00.904 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 09:30:00.967 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 09:30:01.029 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 09:30:01.112 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 09:30:01.164 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 09:30:01.228 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 09:30:01.228 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 09:30:10.602 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 huawei-obs-1 成功
2025-08-18 09:30:24.656 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-18 09:30:24.658 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 72070 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-18 09:30:24.658 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "dev"
2025-08-18 09:30:25.631 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-18 09:30:25.632 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-18 09:30:25.691 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-08-18 09:30:25.882 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=28091920-0671-3787-8121-5082b2e149ac
2025-08-18 09:30:26.083 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 09:30:26.086 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 09:30:26.087 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 09:30:26.293 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8100 (http)
2025-08-18 09:30:26.299 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8100"]
2025-08-18 09:30:26.299 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-18 09:30:26.299 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-18 09:30:26.395 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-18 09:30:26.395 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1673 ms
2025-08-18 09:30:27.113 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-18 09:30:27.134 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-08-18 09:30:27.543 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 09:30:27.641 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 09:30:28.056 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-18 09:30:28.311 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-18 09:30:28.885 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载华为云 OBS 存储平台：huawei-obs-1
2025-08-18 09:30:29.401 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-18 09:30:29.502 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-18 09:30:29.912 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-18 09:30:30.545 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8100"]
2025-08-18 09:30:30.552 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8100 (http) with context path '/api/meeting'
2025-08-18 09:30:30.561 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-18 09:30:30.564 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-18 09:30:30.585 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-18 09:30:30.675 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-08-18 09:30:30.748 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-08-18 09:30:30.754 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-08-18 09:30:30.755 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-08-18 09:30:30.767 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-08-18 09:30:30.778 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-08-18 09:30:30.780 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2025-08-18 09:30:30.783 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-08-18 09:30:30.817 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 09:30:30.822 [main] INFO  cn.july.orch.meeting.Application - Started Application in 6.482 seconds (JVM running for 7.458)
2025-08-18 09:30:30.905 [RMI TCP Connection(2)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 09:30:30.905 [RMI TCP Connection(2)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-18 09:30:30.906 [RMI TCP Connection(2)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-18 09:30:30.937 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 09:30:30.937 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:30:30.938 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:30:31.070 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:30:31.070 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 开始处理超期任务
2025-08-18 09:30:31.098 [scheduling-1] INFO  c.j.o.m.service.TaskActionService - 处理超期任务
2025-08-18 09:30:31.176 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 处理超期任务完成
2025-08-18 09:31:30.822 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:31:30.825 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:31:30.964 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:32:30.823 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:32:30.826 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:32:31.032 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:33:30.822 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:33:30.824 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:33:30.994 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:34:30.822 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:34:30.824 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:34:30.959 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:34:49.553 [http-nio-8100-exec-1] WARN  c.j.o.m.c.MeetingPlanController - [logCost][createMeetingPlan][7664][127.0.0.1][/api/meeting/meeting-plan/create], args={}, requestBody={  "planName": "会议规划7",  "plannedStartTime": "2025-08-25 18:53:05",  "plannedEndTime": "2025-08-25 20:53:05",  "meetingLocation": "1",  "meetingStandardId": 1,  "priorityLevel": 1,  "attendees": [    "ou_ca11635b2ad48203b793d90d78f10a0e",    "ou_755ea7451f97828ca8038c179d0217e2",    "ou_b6673c6de16f2864289f8e06b02f4be7"  ],  "planDescription": "啊啊啊"}, result={"success":true,"traceid":"","code":"000000200","msg":"success","timestamp":1755480889534}
2025-08-18 09:35:30.818 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 09:35:30.819 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 09:35:30.972 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 09:35:31.454 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 huawei-obs-1 成功
2025-08-18 21:05:50.241 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-18 21:05:50.243 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 7655 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-18 21:05:50.244 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "dev"
2025-08-18 21:05:51.261 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-18 21:05:51.262 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-18 21:05:51.317 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-18 21:05:51.514 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=004e10b0-587b-30f0-aea8-c678e014520a
2025-08-18 21:05:51.804 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:05:51.807 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:05:51.809 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:05:52.066 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8100 (http)
2025-08-18 21:05:52.082 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8100"]
2025-08-18 21:05:52.082 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-18 21:05:52.082 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-18 21:05:52.212 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-18 21:05:52.212 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1893 ms
2025-08-18 21:05:53.044 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-18 21:05:53.080 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-08-18 21:05:53.540 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 21:05:53.653 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 21:05:54.113 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-18 21:05:55.052 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-18 21:05:55.584 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载华为云 OBS 存储平台：huawei-obs-1
2025-08-18 21:05:57.203 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:05:57.254 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-18 21:05:57.359 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-18 21:05:57.771 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-18 21:05:59.382 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:05:59.622 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8100"]
2025-08-18 21:05:59.630 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8100 (http) with context path '/api/meeting'
2025-08-18 21:06:00.645 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:06:01.650 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:06:01.653 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-18 21:06:01.698 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-18 21:06:01.727 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-18 21:06:01.847 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-08-18 21:06:01.945 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-08-18 21:06:01.951 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-08-18 21:06:01.952 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-08-18 21:06:01.968 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-08-18 21:06:01.983 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-08-18 21:06:01.987 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2025-08-18 21:06:01.989 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-08-18 21:06:02.035 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:06:02.042 [main] INFO  cn.july.orch.meeting.Application - Started Application in 13.174 seconds (JVM running for 14.511)
2025-08-18 21:06:02.204 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到4个需要生成文本的会议
2025-08-18 21:06:02.205 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:06:02.370 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:06:02.403 [RMI TCP Connection(8)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 21:06:02.403 [RMI TCP Connection(8)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-18 21:06:02.405 [RMI TCP Connection(8)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-18 21:06:02.816 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:06:02.819 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:02.821 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：5，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:02.821 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:06:02.821 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:06:02.867 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:06:02.956 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:06:02.956 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:02.956 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：4，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:02.956 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:06:02.956 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:06:02.998 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:06:03.109 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:06:03.110 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:03.110 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：3，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:03.110 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:06:03.110 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：2，妙计链接：https://meetings.feishu.cn/minutes/obcnb1tcz6997o8i777sth95
2025-08-18 21:06:03.150 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnb1tcz6997o8i777sth95"}
2025-08-18 21:06:03.599 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {}, took 448 ms.
2025-08-18 21:06:03.693 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 新增会议纪要，会议ID：2
2025-08-18 21:06:03.693 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 会议纪要保存成功，会议ID：2
2025-08-18 21:06:03.694 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：2
2025-08-18 21:06:03.694 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了4个会议
2025-08-18 21:06:03.694 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 21:06:03.716 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 21:06:03.716 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:06:03.716 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:06:03.840 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:06:03.841 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 开始处理超期任务
2025-08-18 21:06:03.866 [scheduling-1] INFO  c.j.o.m.service.TaskActionService - 处理超期任务
2025-08-18 21:06:03.931 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 处理超期任务完成
2025-08-18 21:06:45.951 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 手动触发妙计视频转文字任务
2025-08-18 21:06:45.952 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:06:46.043 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到3个需要生成文本的会议
2025-08-18 21:06:46.043 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:06:46.085 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:06:46.192 [http-nio-8100-exec-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:06:46.193 [http-nio-8100-exec-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.manualGenerateMinuteText(MinuteTextGenerationTask.java:130)
	at cn.july.orch.meeting.controller.TestController.testGenerateMinuteText(TestController.java:44)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:61)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:46.194 [http-nio-8100-exec-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：5，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.manualGenerateMinuteText(MinuteTextGenerationTask.java:130)
	at cn.july.orch.meeting.controller.TestController.testGenerateMinuteText(TestController.java:44)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:61)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:46.196 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:06:46.196 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:06:46.239 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:06:46.336 [http-nio-8100-exec-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:06:46.339 [http-nio-8100-exec-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.manualGenerateMinuteText(MinuteTextGenerationTask.java:130)
	at cn.july.orch.meeting.controller.TestController.testGenerateMinuteText(TestController.java:44)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:61)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:46.340 [http-nio-8100-exec-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：4，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.manualGenerateMinuteText(MinuteTextGenerationTask.java:130)
	at cn.july.orch.meeting.controller.TestController.testGenerateMinuteText(TestController.java:44)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:61)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:46.340 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:06:46.340 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:06:46.383 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:06:46.476 [http-nio-8100-exec-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:06:46.477 [http-nio-8100-exec-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.manualGenerateMinuteText(MinuteTextGenerationTask.java:130)
	at cn.july.orch.meeting.controller.TestController.testGenerateMinuteText(TestController.java:44)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:61)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:46.478 [http-nio-8100-exec-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：3，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.manualGenerateMinuteText(MinuteTextGenerationTask.java:130)
	at cn.july.orch.meeting.controller.TestController.testGenerateMinuteText(TestController.java:44)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:61)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:06:46.478 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:06:46.478 [http-nio-8100-exec-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了3个会议
2025-08-18 21:06:46.525 [http-nio-8100-exec-1] INFO  c.j.o.m.controller.TestController - [logCost][testGenerateMinuteText][568][127.0.0.1][/api/meeting/test/generate-minute-text], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"妙计视频转文字定时任务已手动触发","timestamp":1755522406493}
2025-08-18 21:07:02.040 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:07:02.041 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:07:02.237 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:08:02.039 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:08:02.045 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:08:02.353 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:09:02.037 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:09:02.040 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:09:02.348 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:10:00.013 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 21:10:00.733 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 21:10:00.780 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 21:10:00.825 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 21:10:00.891 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 21:10:00.936 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 21:10:00.992 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 21:10:01.032 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 21:10:01.071 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 21:10:01.117 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 21:10:01.156 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 21:10:01.196 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 21:10:01.239 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 21:10:01.304 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 21:10:01.355 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 21:10:01.364 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 21:10:02.041 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:10:02.042 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:10:02.365 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:11:02.041 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:11:02.045 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:11:02.211 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:12:02.046 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:12:02.050 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:12:02.258 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:13:02.045 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:13:02.048 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:13:02.192 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:14:02.044 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:14:02.056 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:14:02.371 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:15:02.043 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:15:02.045 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:15:05.930 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:16:02.046 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:16:02.127 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到3个需要生成文本的会议
2025-08-18 21:16:02.127 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:16:02.182 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:16:02.482 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:16:02.483 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:16:02.483 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：5，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:16:02.483 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:16:02.483 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:16:02.529 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:16:02.631 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:16:02.631 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:16:02.632 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：4，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:16:02.632 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:16:02.633 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:16:02.681 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:16:02.742 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:16:02.742 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:16:02.742 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：3，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:16:02.742 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:16:02.742 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了3个会议
2025-08-18 21:16:02.742 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 21:16:02.935 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 21:16:02.936 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:16:02.936 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:16:03.064 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:17:02.046 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:17:02.048 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:17:02.399 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:18:02.041 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:18:02.043 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:18:02.357 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:19:02.045 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:19:02.046 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:19:02.338 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:20:00.016 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 21:20:00.672 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 21:20:00.712 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 21:20:00.755 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 21:20:00.796 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 21:20:00.842 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 21:20:00.892 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 21:20:00.935 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 21:20:00.976 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 21:20:01.017 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 21:20:01.061 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 21:20:01.105 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 21:20:01.149 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 21:20:01.204 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 21:20:01.244 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 21:20:01.244 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 21:20:02.049 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:20:02.049 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:20:02.343 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:21:02.045 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:21:02.047 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:21:02.351 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:22:02.045 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:22:02.047 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:22:02.343 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:23:02.045 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:23:02.046 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:23:02.204 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:24:02.046 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:24:02.047 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:24:02.360 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:25:02.046 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:25:02.048 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:25:02.471 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:26:02.048 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:26:02.213 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到3个需要生成文本的会议
2025-08-18 21:26:02.213 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:26:02.256 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:26:02.460 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:26:02.461 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:26:02.462 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：5，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:26:02.462 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:26:02.462 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:26:02.507 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:26:02.889 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:26:02.889 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:26:02.889 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：4，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:26:02.890 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:26:02.890 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:26:02.929 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:26:03.009 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:26:03.010 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:26:03.011 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：3，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:26:03.012 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:26:03.012 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了3个会议
2025-08-18 21:26:03.012 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 21:26:03.203 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 21:26:03.203 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:26:03.203 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:26:03.317 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:27:02.053 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:27:02.059 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:27:02.205 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:28:02.047 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:28:02.055 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:28:02.344 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:29:02.088 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:29:02.089 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:29:02.387 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:30:00.006 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 21:30:00.669 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 21:30:00.709 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 21:30:00.750 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 21:30:00.791 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 21:30:00.836 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 21:30:00.877 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 21:30:00.917 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 21:30:00.959 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 21:30:01.000 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 21:30:01.042 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 21:30:01.080 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 21:30:01.122 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 21:30:01.163 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 21:30:01.201 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 21:30:01.202 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 21:30:02.045 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:30:02.045 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:30:02.335 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:31:02.044 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:31:02.046 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:31:02.222 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:32:02.045 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:32:02.046 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:32:02.204 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:33:02.042 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:33:02.044 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:33:02.197 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:34:02.041 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:34:02.043 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:34:02.334 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:35:02.041 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:35:02.044 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:35:02.325 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:36:02.041 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:36:02.140 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到3个需要生成文本的会议
2025-08-18 21:36:02.140 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:36:02.276 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:36:02.665 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:36:02.666 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:36:02.670 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：5，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:36:02.673 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:36:02.676 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:36:02.716 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:36:02.836 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:36:02.836 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:36:02.837 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：4，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:36:02.837 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:36:02.837 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:36:02.893 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:36:02.994 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:36:02.994 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:36:02.995 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：3，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:36:02.996 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:36:02.996 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了3个会议
2025-08-18 21:36:02.996 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 21:36:03.038 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 21:36:03.039 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:36:03.039 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:36:03.173 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:37:02.041 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:37:02.042 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:37:02.220 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:38:02.044 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:38:02.045 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:38:02.331 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:39:02.041 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:39:02.042 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:39:02.354 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:39:07.682 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 huawei-obs-1 成功
2025-08-18 21:39:14.696 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-18 21:39:14.698 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 9802 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-18 21:39:14.698 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "dev"
2025-08-18 21:39:15.684 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-18 21:39:15.686 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-18 21:39:15.764 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-08-18 21:39:15.980 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c123f45-4fff-39a4-a6ac-78c2f654ff10
2025-08-18 21:39:16.211 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:39:16.213 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:39:16.215 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:39:16.445 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8100 (http)
2025-08-18 21:39:16.450 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8100"]
2025-08-18 21:39:16.451 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-18 21:39:16.451 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-18 21:39:16.541 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-18 21:39:16.542 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1785 ms
2025-08-18 21:39:17.287 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-18 21:39:17.310 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-08-18 21:39:17.748 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 21:39:17.827 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 21:39:18.252 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-18 21:39:18.672 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-18 21:39:19.182 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载华为云 OBS 存储平台：huawei-obs-1
2025-08-18 21:39:20.693 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:39:20.750 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-18 21:39:20.885 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-18 21:39:21.305 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-18 21:39:22.749 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:39:23.025 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8100"]
2025-08-18 21:39:23.032 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8100 (http) with context path '/api/meeting'
2025-08-18 21:39:24.037 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:39:25.043 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:39:25.043 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-18 21:39:25.049 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-18 21:39:25.073 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-18 21:39:25.180 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-08-18 21:39:25.260 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-08-18 21:39:25.266 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-08-18 21:39:25.267 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-08-18 21:39:25.279 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-08-18 21:39:25.292 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-08-18 21:39:25.295 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2025-08-18 21:39:25.297 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-08-18 21:39:25.331 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:39:25.336 [main] INFO  cn.july.orch.meeting.Application - Started Application in 11.969 seconds (JVM running for 13.026)
2025-08-18 21:39:25.480 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到3个需要生成文本的会议
2025-08-18 21:39:25.480 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:39:25.557 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:39:25.821 [RMI TCP Connection(6)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 21:39:25.821 [RMI TCP Connection(6)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-18 21:39:25.823 [RMI TCP Connection(6)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-18 21:39:26.195 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:39:26.197 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:39:26.197 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：5，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:39:26.197 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:39:26.197 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:39:26.406 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:39:26.493 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:39:26.493 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:39:26.493 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：4，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:39:26.493 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:39:26.493 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:39:26.534 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:39:26.597 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:39:26.598 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:39:26.598 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：3，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:39:26.598 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:39:26.598 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了3个会议
2025-08-18 21:39:26.599 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:39:26.644 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到3个需要补充实际信息的会议
2025-08-18 21:39:26.644 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：3，飞书会议ID：7539757095179304964
2025-08-18 21:39:26.648 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539757095179304964"}
2025-08-18 21:39:26.945 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539757095179304964","topic":"五点会议","url":null,"meetingNo":"114935442","password":null,"createTime":"1755486630","startTime":"1755486630","endTime":"1755486653","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755486630","finalLeaveTime":"1755486653","inMeetingDuration":"23","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 289 ms.
2025-08-18 21:39:26.946 [scheduling-1] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：3，时间：1755486630
2025-08-18 21:39:26.946 [scheduling-1] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：3，时间：1755486653
2025-08-18 21:39:27.011 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：3，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:39:27.011 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：4，飞书会议ID：7539759129431932931
2025-08-18 21:39:27.011 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539759129431932931"}
2025-08-18 21:39:27.159 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539759129431932931","topic":"11点会议","url":null,"meetingNo":"976394698","password":null,"createTime":"1755487080","startTime":"1755487080","endTime":"1755487112","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755487080","finalLeaveTime":"1755487112","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 147 ms.
2025-08-18 21:39:27.159 [scheduling-1] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：4，时间：1755487080
2025-08-18 21:39:27.159 [scheduling-1] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：4，时间：1755487112
2025-08-18 21:39:27.202 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：4，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:39:27.203 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：5，飞书会议ID：7539828257023410195
2025-08-18 21:39:27.203 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539828257023410195"}
2025-08-18 21:39:27.338 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539828257023410195","topic":"4点会议","url":null,"meetingNo":"516639773","password":null,"createTime":"1755503169","startTime":"1755503169","endTime":"1755503201","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755503169","finalLeaveTime":"1755503201","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 134 ms.
2025-08-18 21:39:27.338 [scheduling-1] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：5，时间：1755503169
2025-08-18 21:39:27.338 [scheduling-1] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：5，时间：1755503201
2025-08-18 21:39:27.381 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：5，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:39:27.382 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：3个，失败：0个
2025-08-18 21:39:27.382 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 21:39:27.406 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 21:39:27.406 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:39:27.406 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:39:27.535 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:39:27.535 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 开始处理超期任务
2025-08-18 21:39:27.558 [scheduling-1] INFO  c.j.o.m.service.TaskActionService - 处理超期任务
2025-08-18 21:39:27.624 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 处理超期任务完成
2025-08-18 21:39:29.770 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:39:29.770 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:39:29.819 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到0个需要补充实际信息的会议
2025-08-18 21:39:29.819 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：0个，失败：0个
2025-08-18 21:39:29.840 [http-nio-8100-exec-1] INFO  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][67][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755524369826}
2025-08-18 21:40:00.009 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 21:40:00.802 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 21:40:00.853 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 21:40:00.902 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 21:40:00.956 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 21:40:01.016 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 21:40:01.063 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 21:40:01.116 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 21:40:01.168 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 21:40:01.209 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 21:40:01.247 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 21:40:01.300 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 21:40:01.348 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 21:40:01.392 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 21:40:01.432 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 21:40:01.433 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 21:40:25.337 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:40:25.338 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:40:25.624 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:41:25.337 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:41:25.338 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:41:25.671 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:42:25.345 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:42:25.347 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:42:25.631 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:42:47.160 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:42:47.161 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:42:47.214 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到5个需要补充实际信息的会议
2025-08-18 21:42:47.214 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：1，飞书会议ID：7539382603205181459
2025-08-18 21:42:47.215 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539382603205181459"}
2025-08-18 21:42:47.602 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539382603205181459","topic":"测试会议","url":null,"meetingNo":"685971515","password":null,"createTime":"1755399912","startTime":"1755399912","endTime":"1755399927","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755399912","finalLeaveTime":"1755399927","inMeetingDuration":"15","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 386 ms.
2025-08-18 21:42:47.602 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：1，时间：1755399912
2025-08-18 21:42:47.602 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：1，时间：1755399927
2025-08-18 21:42:47.649 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：1，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:42:47.649 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：2，飞书会议ID：7539386548623163395
2025-08-18 21:42:47.649 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539386548623163395"}
2025-08-18 21:42:47.930 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539386548623163395","topic":"测试会议2","url":null,"meetingNo":"159916819","password":null,"createTime":"1755400785","startTime":"1755400785","endTime":"1755400870","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"3","participantCountAccumulated":"3","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755400785","finalLeaveTime":"1755400870","inMeetingDuration":"85","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_b6673c6de16f2864289f8e06b02f4be7","firstJoinTime":"1755400847","finalLeaveTime":"1755400870","inMeetingDuration":"23","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755400862","finalLeaveTime":"1755400870","inMeetingDuration":"8","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 281 ms.
2025-08-18 21:42:47.931 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：2，时间：1755400785
2025-08-18 21:42:47.931 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：2，时间：1755400870
2025-08-18 21:42:47.974 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：2，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：3
2025-08-18 21:42:47.974 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：3，飞书会议ID：7539757095179304964
2025-08-18 21:42:47.974 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539757095179304964"}
2025-08-18 21:42:48.190 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539757095179304964","topic":"五点会议","url":null,"meetingNo":"114935442","password":null,"createTime":"1755486630","startTime":"1755486630","endTime":"1755486653","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755486630","finalLeaveTime":"1755486653","inMeetingDuration":"23","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 216 ms.
2025-08-18 21:42:48.191 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：3，时间：1755486630
2025-08-18 21:42:48.191 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：3，时间：1755486653
2025-08-18 21:42:48.249 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：3，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:42:48.249 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：4，飞书会议ID：7539759129431932931
2025-08-18 21:42:48.249 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539759129431932931"}
2025-08-18 21:42:48.442 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539759129431932931","topic":"11点会议","url":null,"meetingNo":"976394698","password":null,"createTime":"1755487080","startTime":"1755487080","endTime":"1755487112","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755487080","finalLeaveTime":"1755487112","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 193 ms.
2025-08-18 21:42:48.443 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：4，时间：1755487080
2025-08-18 21:42:48.443 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：4，时间：1755487112
2025-08-18 21:42:48.491 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：4，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:42:48.491 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：5，飞书会议ID：7539828257023410195
2025-08-18 21:42:48.491 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539828257023410195"}
2025-08-18 21:42:48.619 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539828257023410195","topic":"4点会议","url":null,"meetingNo":"516639773","password":null,"createTime":"1755503169","startTime":"1755503169","endTime":"1755503201","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755503169","finalLeaveTime":"1755503201","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 128 ms.
2025-08-18 21:42:48.619 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：5，时间：1755503169
2025-08-18 21:42:48.619 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：5，时间：1755503201
2025-08-18 21:42:48.691 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：5，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:42:48.691 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：5个，失败：0个
2025-08-18 21:42:48.693 [http-nio-8100-exec-2] WARN  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][1534][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755524568692}
2025-08-18 21:43:21.607 [http-nio-8100-exec-4] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:43:21.607 [http-nio-8100-exec-4] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:43:21.654 [http-nio-8100-exec-4] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到0个需要补充实际信息的会议
2025-08-18 21:43:31.213 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:43:31.213 [http-nio-8100-exec-4] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：0个，失败：0个
2025-08-18 21:43:31.214 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:43:31.222 [http-nio-8100-exec-4] WARN  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][9615][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755524611217}
2025-08-18 21:43:31.386 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:43:39.924 [http-nio-8100-exec-6] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:43:39.928 [http-nio-8100-exec-6] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:43:39.985 [http-nio-8100-exec-6] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到0个需要补充实际信息的会议
2025-08-18 21:43:43.696 [http-nio-8100-exec-6] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：0个，失败：0个
2025-08-18 21:43:43.700 [http-nio-8100-exec-6] WARN  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][3780][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755524623698}
2025-08-18 21:43:54.881 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:43:54.882 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:43:54.942 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到5个需要补充实际信息的会议
2025-08-18 21:43:54.943 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：1，飞书会议ID：7539382603205181459
2025-08-18 21:43:54.943 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539382603205181459"}
2025-08-18 21:43:55.167 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539382603205181459","topic":"测试会议","url":null,"meetingNo":"685971515","password":null,"createTime":"1755399912","startTime":"1755399912","endTime":"1755399927","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755399912","finalLeaveTime":"1755399927","inMeetingDuration":"15","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 223 ms.
2025-08-18 21:43:55.168 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：1，时间：1755399912
2025-08-18 21:43:55.168 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：1，时间：1755399927
2025-08-18 21:43:55.226 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：1，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:43:55.226 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：2，飞书会议ID：7539386548623163395
2025-08-18 21:43:55.226 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539386548623163395"}
2025-08-18 21:43:55.399 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539386548623163395","topic":"测试会议2","url":null,"meetingNo":"159916819","password":null,"createTime":"1755400785","startTime":"1755400785","endTime":"1755400870","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"3","participantCountAccumulated":"3","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755400785","finalLeaveTime":"1755400870","inMeetingDuration":"85","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_b6673c6de16f2864289f8e06b02f4be7","firstJoinTime":"1755400847","finalLeaveTime":"1755400870","inMeetingDuration":"23","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755400862","finalLeaveTime":"1755400870","inMeetingDuration":"8","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 172 ms.
2025-08-18 21:43:55.399 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：2，时间：1755400785
2025-08-18 21:43:55.399 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：2，时间：1755400870
2025-08-18 21:43:55.459 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：2，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：3
2025-08-18 21:43:55.459 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：3，飞书会议ID：7539757095179304964
2025-08-18 21:43:55.459 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539757095179304964"}
2025-08-18 21:43:55.665 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539757095179304964","topic":"五点会议","url":null,"meetingNo":"114935442","password":null,"createTime":"1755486630","startTime":"1755486630","endTime":"1755486653","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755486630","finalLeaveTime":"1755486653","inMeetingDuration":"23","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 206 ms.
2025-08-18 21:43:55.665 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：3，时间：1755486630
2025-08-18 21:43:55.666 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：3，时间：1755486653
2025-08-18 21:43:55.724 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：3，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:43:55.724 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：4，飞书会议ID：7539759129431932931
2025-08-18 21:43:55.724 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539759129431932931"}
2025-08-18 21:43:56.106 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539759129431932931","topic":"11点会议","url":null,"meetingNo":"976394698","password":null,"createTime":"1755487080","startTime":"1755487080","endTime":"1755487112","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755487080","finalLeaveTime":"1755487112","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 381 ms.
2025-08-18 21:43:56.107 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：4，时间：1755487080
2025-08-18 21:43:56.107 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：4，时间：1755487112
2025-08-18 21:43:56.153 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：4，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:43:56.153 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：5，飞书会议ID：7539828257023410195
2025-08-18 21:43:56.153 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539828257023410195"}
2025-08-18 21:43:56.288 [http-nio-8100-exec-8] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539828257023410195","topic":"4点会议","url":null,"meetingNo":"516639773","password":null,"createTime":"1755503169","startTime":"1755503169","endTime":"1755503201","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755503169","finalLeaveTime":"1755503201","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 135 ms.
2025-08-18 21:43:56.288 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：5，时间：1755503169
2025-08-18 21:43:56.288 [http-nio-8100-exec-8] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：5，时间：1755503201
2025-08-18 21:43:56.345 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：5，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:43:56.345 [http-nio-8100-exec-8] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：5个，失败：0个
2025-08-18 21:43:56.346 [http-nio-8100-exec-8] WARN  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][1465][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755524636346}
2025-08-18 21:44:17.702 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:44:17.702 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:44:17.753 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到5个需要补充实际信息的会议
2025-08-18 21:44:42.443 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:44:42.444 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=53s608ms).
2025-08-18 21:44:42.445 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:44:42.445 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：1，飞书会议ID：7539382603205181459
2025-08-18 21:44:42.447 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539382603205181459"}
2025-08-18 21:44:42.593 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:44:42.598 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539382603205181459","topic":"测试会议","url":null,"meetingNo":"685971515","password":null,"createTime":"1755399912","startTime":"1755399912","endTime":"1755399927","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755399912","finalLeaveTime":"1755399927","inMeetingDuration":"15","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 151 ms.
2025-08-18 21:45:09.243 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：1，时间：1755399912
2025-08-18 21:45:09.244 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：1，时间：1755399927
2025-08-18 21:45:22.288 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：1，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:45:22.290 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：2，飞书会议ID：7539386548623163395
2025-08-18 21:45:22.290 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539386548623163395"}
2025-08-18 21:45:22.433 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539386548623163395","topic":"测试会议2","url":null,"meetingNo":"159916819","password":null,"createTime":"1755400785","startTime":"1755400785","endTime":"1755400870","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"3","participantCountAccumulated":"3","participants":[{"id":"ou_b6673c6de16f2864289f8e06b02f4be7","firstJoinTime":"1755400847","finalLeaveTime":"1755400870","inMeetingDuration":"23","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755400862","finalLeaveTime":"1755400870","inMeetingDuration":"8","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755400785","finalLeaveTime":"1755400870","inMeetingDuration":"85","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 142 ms.
2025-08-18 21:45:28.927 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:45:30.015 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:45:46.941 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：2，时间：1755400785
2025-08-18 21:45:46.944 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：2，时间：1755400870
2025-08-18 21:45:48.022 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:45:52.927 [http-nio-8100-exec-10] WARN  com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@65793797 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-08-18 21:45:53.025 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：2，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：3
2025-08-18 21:45:53.026 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：3，飞书会议ID：7539757095179304964
2025-08-18 21:45:53.027 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539757095179304964"}
2025-08-18 21:45:53.182 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539757095179304964","topic":"五点会议","url":null,"meetingNo":"114935442","password":null,"createTime":"1755486630","startTime":"1755486630","endTime":"1755486653","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755486630","finalLeaveTime":"1755486653","inMeetingDuration":"23","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 152 ms.
2025-08-18 21:45:54.795 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：3，时间：1755486630
2025-08-18 21:45:54.796 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：3，时间：1755486653
2025-08-18 21:45:54.869 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：3，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:45:54.869 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：4，飞书会议ID：7539759129431932931
2025-08-18 21:45:54.869 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539759129431932931"}
2025-08-18 21:45:55.008 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539759129431932931","topic":"11点会议","url":null,"meetingNo":"976394698","password":null,"createTime":"1755487080","startTime":"1755487080","endTime":"1755487112","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755487080","finalLeaveTime":"1755487112","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 139 ms.
2025-08-18 21:45:57.839 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：4，时间：1755487080
2025-08-18 21:45:57.839 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：4，时间：1755487112
2025-08-18 21:45:57.907 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：4，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:45:57.907 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：5，飞书会议ID：7539828257023410195
2025-08-18 21:45:57.907 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539828257023410195"}
2025-08-18 21:45:58.046 [http-nio-8100-exec-10] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539828257023410195","topic":"4点会议","url":null,"meetingNo":"516639773","password":null,"createTime":"1755503169","startTime":"1755503169","endTime":"1755503201","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755503169","finalLeaveTime":"1755503201","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 138 ms.
2025-08-18 21:45:58.046 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：5，时间：1755503169
2025-08-18 21:45:58.046 [http-nio-8100-exec-10] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：5，时间：1755503201
2025-08-18 21:45:58.106 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：5，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:45:58.106 [http-nio-8100-exec-10] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：5个，失败：0个
2025-08-18 21:45:58.109 [http-nio-8100-exec-10] WARN  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][100408][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755524758107}
2025-08-18 21:46:25.346 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:46:25.348 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:46:25.511 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:46:40.300 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:46:40.300 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:46:40.359 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到5个需要补充实际信息的会议
2025-08-18 21:46:50.699 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：1，飞书会议ID：7539382603205181459
2025-08-18 21:46:50.701 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539382603205181459"}
2025-08-18 21:46:50.845 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539382603205181459","topic":"测试会议","url":null,"meetingNo":"685971515","password":null,"createTime":"1755399912","startTime":"1755399912","endTime":"1755399927","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755399912","finalLeaveTime":"1755399927","inMeetingDuration":"15","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 142 ms.
2025-08-18 21:47:00.604 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：1，时间：1755399912
2025-08-18 21:47:00.604 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：1，时间：1755399927
2025-08-18 21:47:00.667 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：1，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:47:00.668 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：2，飞书会议ID：7539386548623163395
2025-08-18 21:47:00.668 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539386548623163395"}
2025-08-18 21:47:00.835 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539386548623163395","topic":"测试会议2","url":null,"meetingNo":"159916819","password":null,"createTime":"1755400785","startTime":"1755400785","endTime":"1755400870","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"3","participantCountAccumulated":"3","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755400785","finalLeaveTime":"1755400870","inMeetingDuration":"85","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_b6673c6de16f2864289f8e06b02f4be7","firstJoinTime":"1755400847","finalLeaveTime":"1755400870","inMeetingDuration":"23","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755400862","finalLeaveTime":"1755400870","inMeetingDuration":"8","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 167 ms.
2025-08-18 21:47:14.276 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：2，时间：1755400785
2025-08-18 21:47:14.278 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：2，时间：1755400870
2025-08-18 21:47:14.355 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：2，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：3
2025-08-18 21:47:14.355 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：3，飞书会议ID：7539757095179304964
2025-08-18 21:47:14.355 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539757095179304964"}
2025-08-18 21:47:14.490 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539757095179304964","topic":"五点会议","url":null,"meetingNo":"114935442","password":null,"createTime":"1755486630","startTime":"1755486630","endTime":"1755486653","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755486630","finalLeaveTime":"1755486653","inMeetingDuration":"23","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 135 ms.
2025-08-18 21:47:14.490 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：3，时间：1755486630
2025-08-18 21:47:14.490 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：3，时间：1755486653
2025-08-18 21:47:14.542 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：3，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:47:14.542 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：4，飞书会议ID：7539759129431932931
2025-08-18 21:47:14.542 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539759129431932931"}
2025-08-18 21:47:14.736 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539759129431932931","topic":"11点会议","url":null,"meetingNo":"976394698","password":null,"createTime":"1755487080","startTime":"1755487080","endTime":"1755487112","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755487080","finalLeaveTime":"1755487112","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 194 ms.
2025-08-18 21:47:14.736 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：4，时间：1755487080
2025-08-18 21:47:14.736 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：4，时间：1755487112
2025-08-18 21:47:14.787 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：4，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:47:14.787 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：5，飞书会议ID：7539828257023410195
2025-08-18 21:47:14.787 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539828257023410195"}
2025-08-18 21:47:14.962 [http-nio-8100-exec-2] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539828257023410195","topic":"4点会议","url":null,"meetingNo":"516639773","password":null,"createTime":"1755503169","startTime":"1755503169","endTime":"1755503201","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755503169","finalLeaveTime":"1755503201","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 174 ms.
2025-08-18 21:47:14.962 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际开始时间失败，会议ID：5，时间：1755503169
2025-08-18 21:47:14.962 [http-nio-8100-exec-2] WARN  c.j.o.m.j.MeetingActualInfoSupplementTask - 解析实际结束时间失败，会议ID：5，时间：1755503201
2025-08-18 21:47:15.005 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：5，实际开始时间：未更新，实际结束时间：未更新，实际参会人数：1
2025-08-18 21:47:15.006 [http-nio-8100-exec-2] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：5个，失败：0个
2025-08-18 21:47:15.008 [http-nio-8100-exec-2] WARN  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][34709][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755524835006}
2025-08-18 21:47:25.350 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:47:25.352 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:47:25.495 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:48:25.351 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:48:25.356 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:48:25.662 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:49:25.348 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:49:25.421 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到3个需要生成文本的会议
2025-08-18 21:49:25.421 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:49:25.468 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:49:25.712 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:49:25.712 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:49:25.713 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：5，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:49:25.713 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:49:25.713 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:49:25.752 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:49:25.808 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:49:25.808 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:49:25.808 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：4，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:49:25.808 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:49:25.808 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:49:25.846 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:49:25.933 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request failed, code: 99991679, msg: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
2025-08-18 21:49:25.933 [scheduling-1] ERROR cn.july.feishu.util.FeishuInvokeUtil - Feishu Request error: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
cn.july.feishu.exception.ThirdException: Unauthorized. You do not have permission to perform the requested operation on the resource. Please request user re-authorization and try again. required one of these privileges under the user identity: [minutes:minute:download, minutes:minutes.transcript:export].应用未获取所需的用户授权：[minutes:minute:download, minutes:minutes.transcript:export]，点击链接查看解决方案：https://open.feishu.cn/document/uAjLw4CM/ugTN1YjL4UTN24CO1UjN/trouble-shooting/how-to-resolve-error-99991679
	at cn.july.feishu.util.FeishuInvokeUtil.handleResponse(FeishuInvokeUtil.java:32)
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:55)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:49:25.933 [scheduling-1] ERROR c.j.o.m.s.NewMeetingDomainService - 获取会议纪要失败，会议ID：3，错误：获取妙计转文字失败
cn.july.feishu.exception.ThirdException: 获取妙计转文字失败
	at cn.july.feishu.util.FeishuInvokeUtil.executeRequest(FeishuInvokeUtil.java:58)
	at cn.july.feishu.service.MeetService.getMinuteText(MeetService.java:46)
	at cn.july.orch.meeting.service.NewMeetingDomainService.getMinuteText(NewMeetingDomainService.java:135)
	at cn.july.orch.meeting.job.MinuteTextGenerationTask.generateMinuteText(MinuteTextGenerationTask.java:52)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 21:49:25.933 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:49:25.933 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了3个会议
2025-08-18 21:49:25.933 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 21:49:26.115 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 21:49:26.115 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:49:26.115 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:49:26.224 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:50:00.018 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 21:50:00.638 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 21:50:00.681 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 21:50:00.726 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 21:50:00.766 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 21:50:00.807 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 21:50:00.848 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 21:50:00.888 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 21:50:00.928 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 21:50:00.969 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 21:50:01.008 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 21:50:01.047 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 21:50:01.087 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 21:50:01.133 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 21:50:01.174 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 21:50:01.174 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 21:50:25.349 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:50:25.351 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:50:25.670 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:51:31.324 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-18 21:51:31.326 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 10559 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-18 21:51:31.326 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "dev"
2025-08-18 21:51:32.264 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-18 21:51:32.266 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-18 21:51:32.330 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-08-18 21:51:32.511 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c123f45-4fff-39a4-a6ac-78c2f654ff10
2025-08-18 21:51:32.722 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:51:32.724 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:51:32.726 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 21:51:32.934 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8100 (http)
2025-08-18 21:51:32.939 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8100"]
2025-08-18 21:51:32.940 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-18 21:51:32.940 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-18 21:51:33.037 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-18 21:51:33.038 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1652 ms
2025-08-18 21:51:33.712 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-18 21:51:33.731 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-08-18 21:51:34.146 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 21:51:34.263 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 21:51:34.694 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-18 21:51:35.117 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-18 21:51:35.610 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载华为云 OBS 存储平台：huawei-obs-1
2025-08-18 21:51:37.100 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:51:37.175 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-18 21:51:37.314 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-18 21:51:37.702 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-18 21:51:39.108 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:51:39.360 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8100"]
2025-08-18 21:51:39.367 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8100 (http) with context path '/api/meeting'
2025-08-18 21:51:40.373 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:51:41.381 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 21:51:41.382 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-18 21:51:41.393 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-18 21:51:41.427 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-18 21:51:41.523 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-08-18 21:51:41.598 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-08-18 21:51:41.605 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-08-18 21:51:41.605 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-08-18 21:51:41.617 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-08-18 21:51:41.629 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-08-18 21:51:41.632 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2025-08-18 21:51:41.634 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-08-18 21:51:41.667 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 21:51:41.672 [main] INFO  cn.july.orch.meeting.Application - Started Application in 11.653 seconds (JVM running for 12.654)
2025-08-18 21:51:41.806 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到3个需要生成文本的会议
2025-08-18 21:51:41.806 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：5，妙计链接：https://meetings.feishu.cn/minutes/obcnct3ok2355ncw217x88w1
2025-08-18 21:51:41.890 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnct3ok2355ncw217x88w1"}
2025-08-18 21:51:42.115 [RMI TCP Connection(1)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 21:51:42.115 [RMI TCP Connection(1)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-18 21:51:42.117 [RMI TCP Connection(1)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-18 21:51:42.851 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {}, took 960 ms.
2025-08-18 21:51:42.956 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 新增会议纪要，会议ID：5
2025-08-18 21:51:42.956 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 会议纪要保存成功，会议ID：5
2025-08-18 21:51:42.956 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：5
2025-08-18 21:51:42.957 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：4，妙计链接：https://meetings.feishu.cn/minutes/obcncp9ood7829x7e828d5px
2025-08-18 21:51:42.999 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp9ood7829x7e828d5px"}
2025-08-18 21:51:43.409 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {}, took 410 ms.
2025-08-18 21:51:43.475 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 新增会议纪要，会议ID：4
2025-08-18 21:51:43.475 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 会议纪要保存成功，会议ID：4
2025-08-18 21:51:43.475 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：4
2025-08-18 21:51:43.475 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：3，妙计链接：https://meetings.feishu.cn/minutes/obcncp6tj6qn2q2568c65p92
2025-08-18 21:51:43.523 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcncp6tj6qn2q2568c65p92"}
2025-08-18 21:51:43.849 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {}, took 326 ms.
2025-08-18 21:51:43.913 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 新增会议纪要，会议ID：3
2025-08-18 21:51:43.914 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 会议纪要保存成功，会议ID：3
2025-08-18 21:51:43.914 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：3
2025-08-18 21:51:43.914 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了3个会议
2025-08-18 21:51:43.916 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:51:43.946 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到0个需要补充实际信息的会议
2025-08-18 21:51:43.947 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：0个，失败：0个
2025-08-18 21:51:43.947 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 21:51:43.968 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 21:51:43.968 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:51:43.968 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:51:44.089 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:51:44.089 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 开始处理超期任务
2025-08-18 21:51:44.117 [scheduling-1] INFO  c.j.o.m.service.TaskActionService - 处理超期任务
2025-08-18 21:51:44.182 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 处理超期任务完成
2025-08-18 21:52:41.680 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:52:41.681 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:52:41.841 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:53:27.471 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 手动触发会议实际信息补充任务
2025-08-18 21:53:27.472 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 21:53:27.517 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到5个需要补充实际信息的会议
2025-08-18 21:53:27.517 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：1，飞书会议ID：7539382603205181459
2025-08-18 21:53:27.528 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539382603205181459"}
2025-08-18 21:53:27.838 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539382603205181459","topic":"测试会议","url":null,"meetingNo":"685971515","password":null,"createTime":"1755399912","startTime":"1755399912","endTime":"1755399927","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755399912","finalLeaveTime":"1755399927","inMeetingDuration":"15","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 302 ms.
2025-08-18 21:53:27.892 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：1，实际开始时间：2025-08-17T11:05:12，实际结束时间：2025-08-17T11:05:27，实际参会人数：1
2025-08-18 21:53:27.892 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：2，飞书会议ID：7539386548623163395
2025-08-18 21:53:27.893 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539386548623163395"}
2025-08-18 21:53:28.034 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539386548623163395","topic":"测试会议2","url":null,"meetingNo":"159916819","password":null,"createTime":"1755400785","startTime":"1755400785","endTime":"1755400870","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"3","participantCountAccumulated":"3","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755400785","finalLeaveTime":"1755400870","inMeetingDuration":"85","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_b6673c6de16f2864289f8e06b02f4be7","firstJoinTime":"1755400847","finalLeaveTime":"1755400870","inMeetingDuration":"23","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4},{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755400862","finalLeaveTime":"1755400870","inMeetingDuration":"8","userType":1,"isHost":false,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 141 ms.
2025-08-18 21:53:28.082 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：2，实际开始时间：2025-08-17T11:19:45，实际结束时间：2025-08-17T11:21:10，实际参会人数：3
2025-08-18 21:53:28.082 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：3，飞书会议ID：7539757095179304964
2025-08-18 21:53:28.083 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539757095179304964"}
2025-08-18 21:53:28.199 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539757095179304964","topic":"五点会议","url":null,"meetingNo":"114935442","password":null,"createTime":"1755486630","startTime":"1755486630","endTime":"1755486653","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755486630","finalLeaveTime":"1755486653","inMeetingDuration":"23","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 115 ms.
2025-08-18 21:53:28.244 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：3，实际开始时间：2025-08-18T11:10:30，实际结束时间：2025-08-18T11:10:53，实际参会人数：1
2025-08-18 21:53:28.245 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：4，飞书会议ID：7539759129431932931
2025-08-18 21:53:28.245 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539759129431932931"}
2025-08-18 21:53:28.503 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539759129431932931","topic":"11点会议","url":null,"meetingNo":"976394698","password":null,"createTime":"1755487080","startTime":"1755487080","endTime":"1755487112","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755487080","finalLeaveTime":"1755487112","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 258 ms.
2025-08-18 21:53:28.551 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：4，实际开始时间：2025-08-18T11:18，实际结束时间：2025-08-18T11:18:32，实际参会人数：1
2025-08-18 21:53:28.551 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始补充会议实际信息，会议ID：5，飞书会议ID：7539828257023410195
2025-08-18 21:53:28.552 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539828257023410195"}
2025-08-18 21:53:28.665 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539828257023410195","topic":"4点会议","url":null,"meetingNo":"516639773","password":null,"createTime":"1755503169","startTime":"1755503169","endTime":"1755503201","hostUser":{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_ca11635b2ad48203b793d90d78f10a0e","firstJoinTime":"1755503169","finalLeaveTime":"1755503201","inMeetingDuration":"32","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 112 ms.
2025-08-18 21:53:28.716 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充成功，会议ID：5，实际开始时间：2025-08-18T15:46:09，实际结束时间：2025-08-18T15:46:41，实际参会人数：1
2025-08-18 21:53:28.716 [http-nio-8100-exec-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：5个，失败：0个
2025-08-18 21:53:28.749 [http-nio-8100-exec-1] WARN  c.j.o.m.controller.TestController - [logCost][testSupplementMeetingActualInfo][1275][127.0.0.1][/api/meeting/test/supplement-meeting-actual-info], args={}, result={"success":true,"traceid":"","code":"000000200","msg":"success","err":null,"data":"会议实际信息补充定时任务已手动触发","timestamp":1755525208728}
2025-08-18 21:53:41.672 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:53:41.673 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:53:41.813 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:54:41.683 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:54:41.691 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:54:41.995 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 21:55:41.674 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 21:55:41.677 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 21:55:41.846 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:38:52.334 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-18 22:38:52.337 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 14310 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-18 22:38:52.337 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "dev"
2025-08-18 22:38:53.354 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-18 22:38:53.355 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-18 22:38:53.422 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-08-18 22:38:53.649 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c123f45-4fff-39a4-a6ac-78c2f654ff10
2025-08-18 22:38:53.866 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 22:38:53.868 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 22:38:53.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 22:38:54.079 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8100 (http)
2025-08-18 22:38:54.085 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8100"]
2025-08-18 22:38:54.086 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-18 22:38:54.086 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-18 22:38:54.194 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-18 22:38:54.194 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1777 ms
2025-08-18 22:38:55.007 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-18 22:38:55.027 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-08-18 22:38:55.460 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 22:38:55.570 [redisson-netty-2-9] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 22:38:56.042 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-18 22:38:56.469 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-18 22:38:57.067 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载华为云 OBS 存储平台：huawei-obs-1
2025-08-18 22:38:58.757 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:38:58.814 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-18 22:38:58.976 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-18 22:38:59.470 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-18 22:39:00.980 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:39:01.274 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8100"]
2025-08-18 22:39:01.284 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8100 (http) with context path '/api/meeting'
2025-08-18 22:39:02.291 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:39:03.299 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:39:03.300 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-18 22:39:03.313 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-18 22:39:03.363 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-18 22:39:03.477 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-08-18 22:39:03.575 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-08-18 22:39:03.581 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-08-18 22:39:03.582 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-08-18 22:39:03.594 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-08-18 22:39:03.606 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-08-18 22:39:03.610 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2025-08-18 22:39:03.612 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-08-18 22:39:03.647 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 22:39:03.652 [main] INFO  cn.july.orch.meeting.Application - Started Application in 12.67 seconds (JVM running for 14.032)
2025-08-18 22:39:04.035 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到0个需要生成文本的会议
2025-08-18 22:39:04.040 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了0个会议
2025-08-18 22:39:04.042 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 22:39:04.112 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到0个需要补充实际信息的会议
2025-08-18 22:39:04.113 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：0个，失败：0个
2025-08-18 22:39:04.114 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 22:39:04.252 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 22:39:04.252 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:39:04.253 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:39:04.369 [RMI TCP Connection(3)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 22:39:04.369 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-18 22:39:04.374 [RMI TCP Connection(3)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-08-18 22:39:04.426 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:39:04.427 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 开始处理超期任务
2025-08-18 22:39:04.462 [scheduling-1] INFO  c.j.o.m.service.TaskActionService - 处理超期任务
2025-08-18 22:39:04.557 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 处理超期任务完成
2025-08-18 22:39:59.990 [http-nio-8100-exec-1] INFO  c.j.o.m.s.CallbackActionService - callback command:{"challenge":null,"type":null,"token":null,"schema":"2.0","header":{"token":"vdSzuFDy6WKQW8kOJfPWLfIYNhuPKNQK","event_id":"a055f0a0e26b3610ea8e60e3be786559","event_type":"vc.meeting.meeting_ended_v1","create_time":"1755527570164","app_id":"cli_a80a244ad965d00d","tenant_key":"169cbc4e4758975e"},"event":{"meeting":{"calendar_event_id":"a2cb09e6-fdf2-487f-9cae-af910da3d34d_0","end_time":"1755527570","host_user":{"id":{"open_id":"ou_755ea7451f97828ca8038c179d0217e2","union_id":"on_c8ad7a859e3f803da8753d66fc42c025","user_id":null},"user_role":2,"user_type":1},"id":"7539929469533913089","meeting_no":"855654469","meeting_source":1,"owner":{"id":{"open_id":"ou_755ea7451f97828ca8038c179d0217e2","union_id":"on_c8ad7a859e3f803da8753d66fc42c025","user_id":null},"user_role":2,"user_type":1},"security_setting":{"has_set_security_contacts_and_group":false,"security_level":2},"start_time":"1755527544","topic":"测试会议"},"operator":{"id":{"open_id":"ou_755ea7451f97828ca8038c179d0217e2","union_id":"on_c8ad7a859e3f803da8753d66fc42c025","user_id":null},"user_role":2,"user_type":1}}}
2025-08-18 22:40:00.005 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 22:40:00.105 [http-nio-8100-exec-1] INFO  c.j.o.m.s.NewMeetingActionService - 处理飞书会议结束回调，日程事件ID：a2cb09e6-fdf2-487f-9cae-af910da3d34d_0
2025-08-18 22:40:00.334 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539929469533913089"}
2025-08-18 22:40:00.759 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 22:40:00.800 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 22:40:00.842 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 22:40:00.883 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 22:40:00.925 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 22:40:00.940 [http-nio-8100-exec-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539929469533913089","topic":"测试会议","url":null,"meetingNo":"855654469","password":null,"createTime":"1755527544","startTime":"1755527544","endTime":"1755527570","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755527544","finalLeaveTime":"1755527570","inMeetingDuration":"26","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 595 ms.
2025-08-18 22:40:00.966 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 22:40:01.007 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 22:40:01.008 [http-nio-8100-exec-1] INFO  c.j.o.m.s.NewMeetingActionService - 会议实际执行信息更新成功，会议ID：6，实际开始时间：2025-08-18T22:32:24，实际结束时间：2025-08-18T22:32:50，实际参会人数：1
2025-08-18 22:40:01.008 [http-nio-8100-exec-1] INFO  c.j.o.m.s.NewMeetingActionService - 飞书会议结束回调处理成功，会议ID：6
2025-08-18 22:40:01.047 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 22:40:01.087 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 22:40:01.127 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 22:40:01.141 [http-nio-8100-exec-1] INFO  c.j.o.m.handler.MeetingEndHandler - 找到会议，会议ID：6，会议名称：测试会议
2025-08-18 22:40:01.164 [http-nio-8100-exec-1] INFO  c.j.o.m.s.MeetingEvaluationActionService - 开始发送新会议评价卡片，会议ID：6
2025-08-18 22:40:01.168 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 22:40:19.441 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 22:40:23.942 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 22:40:23.921 [http-nio-8100-exec-1] INFO  c.j.o.m.s.MeetingEvaluationActionService - 新会议评价卡片发送成功，会议ID：6，接收人：张晓龙
2025-08-18 22:40:41.421 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 22:40:42.242 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:42s
2025-08-18 22:40:42.243 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:40:42.243 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:40:55.584 [http-nio-8100-exec-1] ERROR c.j.o.m.handler.MeetingEndHandler - 发送会议评价卡片失败，calendarEventId：a2cb09e6-fdf2-487f-9cae-af910da3d34d_0
java.lang.IndexOutOfBoundsException: Index: 1, Size: 1
	at java.util.ArrayList.rangeCheck(ArrayList.java:659)
	at java.util.ArrayList.get(ArrayList.java:435)
	at cn.july.orch.meeting.service.MeetingEvaluationActionService.sendNewMeetingEvaluationCards(MeetingEvaluationActionService.java:73)
	at cn.july.orch.meeting.service.MeetingEvaluationActionService$$FastClassBySpringCGLIB$$315d5107.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at cn.july.orch.meeting.service.MeetingEvaluationActionService$$EnhancerBySpringCGLIB$$f4a43500.sendNewMeetingEvaluationCards(<generated>)
	at cn.july.orch.meeting.handler.MeetingEndHandler.handle(MeetingEndHandler.java:53)
	at cn.july.orch.meeting.service.CallbackActionService.callback(CallbackActionService.java:52)
	at cn.july.orch.meeting.controller.CallbackController.callback(CallbackController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.request.BufferedHttpServletRequestFilter.doFilterInner(BufferedHttpServletRequestFilter.java:30)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 22:40:55.620 [http-nio-8100-exec-1] WARN  c.j.o.m.c.CallbackController - [logCost][callback][55652][127.0.0.1][/api/meeting/white/event/callback], args={}, requestBody={  "challenge": null,  "type": null,  "token": null,  "schema": "2.0",  "header": {    "token": "vdSzuFDy6WKQW8kOJfPWLfIYNhuPKNQK",    "event_id": "a055f0a0e26b3610ea8e60e3be786559",    "event_type": "vc.meeting.meeting_ended_v1",    "create_time": "1755527570164",    "app_id": "cli_a80a244ad965d00d",    "tenant_key": "169cbc4e4758975e"  },  "event": {    "meeting": {      "calendar_event_id": "a2cb09e6-fdf2-487f-9cae-af910da3d34d_0",      "end_time": "1755527570",      "host_user": {        "id": {          "open_id": "ou_755ea7451f97828ca8038c179d0217e2",          "union_id": "on_c8ad7a859e3f803da8753d66fc42c025",          "user_id": null        },        "user_role": 2,        "user_type": 1      },      "id": "7539929469533913089",      "meeting_no": "855654469",      "meeting_source": 1,      "owner": {        "id": {          "open_id": "ou_755ea7451f97828ca8038c179d0217e2",          "union_id": "on_c8ad7a859e3f803da8753d66fc42c025",          "user_id": null        },        "user_role": 2,        "user_type": 1      },      "security_setting": {        "has_set_security_contacts_and_group": false,        "security_level": 2      },      "start_time": "1755527544",      "topic": "测试会议"    },    "operator": {      "id": {        "open_id": "ou_755ea7451f97828ca8038c179d0217e2",        "union_id": "on_c8ad7a859e3f803da8753d66fc42c025",        "user_id": null      },      "user_role": 2,      "user_type": 1    }  }}, result={}
2025-08-18 22:40:55.792 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:41:03.650 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:41:03.650 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:41:03.784 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:41:16.572 [http-nio-8100-exec-3] INFO  c.j.o.m.s.CallbackActionService - callback command:{"challenge":null,"type":null,"token":null,"schema":"2.0","header":{"token":"vdSzuFDy6WKQW8kOJfPWLfIYNhuPKNQK","event_id":"a055f0a0e26b3610ea8e60e3be786559","event_type":"vc.meeting.meeting_ended_v1","create_time":"1755527570164","app_id":"cli_a80a244ad965d00d","tenant_key":"169cbc4e4758975e"},"event":{"meeting":{"calendar_event_id":"a2cb09e6-fdf2-487f-9cae-af910da3d34d_0","end_time":"1755527570","host_user":{"id":{"open_id":"ou_755ea7451f97828ca8038c179d0217e2","union_id":"on_c8ad7a859e3f803da8753d66fc42c025","user_id":null},"user_role":2,"user_type":1},"id":"7539929469533913089","meeting_no":"855654469","meeting_source":1,"owner":{"id":{"open_id":"ou_755ea7451f97828ca8038c179d0217e2","union_id":"on_c8ad7a859e3f803da8753d66fc42c025","user_id":null},"user_role":2,"user_type":1},"security_setting":{"has_set_security_contacts_and_group":false,"security_level":2},"start_time":"1755527544","topic":"测试会议"},"operator":{"id":{"open_id":"ou_755ea7451f97828ca8038c179d0217e2","union_id":"on_c8ad7a859e3f803da8753d66fc42c025","user_id":null},"user_role":2,"user_type":1}}}
2025-08-18 22:41:16.654 [http-nio-8100-exec-3] INFO  c.j.o.m.s.NewMeetingActionService - 处理飞书会议结束回调，日程事件ID：a2cb09e6-fdf2-487f-9cae-af910da3d34d_0
2025-08-18 22:41:16.718 [http-nio-8100-exec-3] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"withParticipants":true,"withMeetingAbility":null,"userIdType":"open_id","meetingId":"7539929469533913089"}
2025-08-18 22:41:16.926 [http-nio-8100-exec-3] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {"meeting":{"id":"7539929469533913089","topic":"测试会议","url":null,"meetingNo":"855654469","password":null,"createTime":"1755527544","startTime":"1755527544","endTime":"1755527570","hostUser":{"id":"ou_755ea7451f97828ca8038c179d0217e2","userType":1},"meetingConnect":null,"status":3,"participantCount":"1","participantCountAccumulated":"1","participants":[{"id":"ou_755ea7451f97828ca8038c179d0217e2","firstJoinTime":"1755527544","finalLeaveTime":"1755527570","inMeetingDuration":"26","userType":1,"isHost":true,"isCohost":false,"isExternal":false,"status":4}],"ability":null}}, took 207 ms.
2025-08-18 22:41:16.989 [http-nio-8100-exec-3] INFO  c.j.o.m.s.NewMeetingActionService - 会议实际执行信息更新成功，会议ID：6，实际开始时间：2025-08-18T22:32:24，实际结束时间：2025-08-18T22:32:50，实际参会人数：1
2025-08-18 22:41:16.989 [http-nio-8100-exec-3] INFO  c.j.o.m.s.NewMeetingActionService - 飞书会议结束回调处理成功，会议ID：6
2025-08-18 22:41:17.110 [http-nio-8100-exec-3] INFO  c.j.o.m.handler.MeetingEndHandler - 找到会议，会议ID：6，会议名称：测试会议
2025-08-18 22:41:17.129 [http-nio-8100-exec-3] INFO  c.j.o.m.s.MeetingEvaluationActionService - 开始发送新会议评价卡片，会议ID：6
2025-08-18 22:41:32.653 [http-nio-8100-exec-3] INFO  c.j.o.m.s.MeetingEvaluationActionService - 新会议评价卡片发送成功，会议ID：6，接收人：张晓龙
2025-08-18 22:42:14.815 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:42:14.817 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:42:14.854 [http-nio-8100-exec-3] ERROR c.j.o.m.handler.MeetingEndHandler - 发送会议评价卡片失败，calendarEventId：a2cb09e6-fdf2-487f-9cae-af910da3d34d_0
java.lang.IndexOutOfBoundsException: Index: 1, Size: 1
	at java.util.ArrayList.rangeCheck(ArrayList.java:659)
	at java.util.ArrayList.get(ArrayList.java:435)
	at cn.july.orch.meeting.service.MeetingEvaluationActionService.sendNewMeetingEvaluationCards(MeetingEvaluationActionService.java:73)
	at cn.july.orch.meeting.service.MeetingEvaluationActionService$$FastClassBySpringCGLIB$$315d5107.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at cn.july.orch.meeting.service.MeetingEvaluationActionService$$EnhancerBySpringCGLIB$$f4a43500.sendNewMeetingEvaluationCards(<generated>)
	at cn.july.orch.meeting.handler.MeetingEndHandler.handle(MeetingEndHandler.java:53)
	at cn.july.orch.meeting.service.CallbackActionService.callback(CallbackActionService.java:52)
	at cn.july.orch.meeting.controller.CallbackController.callback(CallbackController.java:36)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.july.web.spring.component.request.BufferedHttpServletRequestFilter.doFilterInner(BufferedHttpServletRequestFilter.java:30)
	at cn.july.web.spring.component.base.AbstractPatternRequestFilter.doFilterInternal(AbstractPatternRequestFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
2025-08-18 22:42:14.858 [http-nio-8100-exec-3] WARN  c.j.o.m.c.CallbackController - [logCost][callback][58288][127.0.0.1][/api/meeting/white/event/callback], args={}, requestBody={  "challenge": null,  "type": null,  "token": null,  "schema": "2.0",  "header": {    "token": "vdSzuFDy6WKQW8kOJfPWLfIYNhuPKNQK",    "event_id": "a055f0a0e26b3610ea8e60e3be786559",    "event_type": "vc.meeting.meeting_ended_v1",    "create_time": "1755527570164",    "app_id": "cli_a80a244ad965d00d",    "tenant_key": "169cbc4e4758975e"  },  "event": {    "meeting": {      "calendar_event_id": "a2cb09e6-fdf2-487f-9cae-af910da3d34d_0",      "end_time": "1755527570",      "host_user": {        "id": {          "open_id": "ou_755ea7451f97828ca8038c179d0217e2",          "union_id": "on_c8ad7a859e3f803da8753d66fc42c025",          "user_id": null        },        "user_role": 2,        "user_type": 1      },      "id": "7539929469533913089",      "meeting_no": "855654469",      "meeting_source": 1,      "owner": {        "id": {          "open_id": "ou_755ea7451f97828ca8038c179d0217e2",          "union_id": "on_c8ad7a859e3f803da8753d66fc42c025",          "user_id": null        },        "user_role": 2,        "user_type": 1      },      "security_setting": {        "has_set_security_contacts_and_group": false,        "security_level": 2      },      "start_time": "1755527544",      "topic": "测试会议"    },    "operator": {      "id": {        "open_id": "ou_755ea7451f97828ca8038c179d0217e2",        "union_id": "on_c8ad7a859e3f803da8753d66fc42c025",        "user_id": null      },      "user_role": 2,      "user_type": 1    }  }}, result={}
2025-08-18 22:42:15.106 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:42:33.997 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 huawei-obs-1 成功
2025-08-18 22:51:22.618 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-18 22:51:22.619 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 15368 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-18 22:51:22.619 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "dev"
2025-08-18 22:51:23.605 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-18 22:51:23.606 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-18 22:51:23.678 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-08-18 22:51:23.887 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=9c123f45-4fff-39a4-a6ac-78c2f654ff10
2025-08-18 22:51:24.126 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 22:51:24.128 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 22:51:24.130 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 22:51:24.344 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8100 (http)
2025-08-18 22:51:24.350 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8100"]
2025-08-18 22:51:24.350 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-18 22:51:24.350 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-18 22:51:24.478 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-18 22:51:24.478 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1781 ms
2025-08-18 22:51:25.235 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-18 22:51:25.267 [main] ERROR i.n.r.d.DnsServerAddressStreamProviders - Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-08-18 22:51:25.698 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 22:51:25.789 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for ************/************:6379
2025-08-18 22:51:26.238 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-18 22:51:26.682 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-18 22:51:27.271 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载华为云 OBS 存储平台：huawei-obs-1
2025-08-18 22:51:28.799 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:51:28.872 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-18 22:51:29.002 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-18 22:51:29.412 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-18 22:51:30.858 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:51:31.135 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8100"]
2025-08-18 22:51:31.143 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8100 (http) with context path '/api/meeting'
2025-08-18 22:51:32.149 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:51:33.156 [main] INFO  o.s.cloud.commons.util.InetUtils - Cannot determine local hostname
2025-08-18 22:51:33.156 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-18 22:51:33.161 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-18 22:51:33.186 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-18 22:51:33.304 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: getFileUrlUsingPOST_1
2025-08-18 22:51:33.392 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: detailUsingGET_1
2025-08-18 22:51:33.398 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_1
2025-08-18 22:51:33.399 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_1
2025-08-18 22:51:33.417 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: pageUsingPOST_1
2025-08-18 22:51:33.437 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: createUsingPOST_2
2025-08-18 22:51:33.441 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: deleteUsingPOST_2
2025-08-18 22:51:33.443 [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: updateUsingPOST_1
2025-08-18 22:51:33.481 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 22:51:33.487 [main] INFO  cn.july.orch.meeting.Application - Started Application in 12.23 seconds (JVM running for 13.25)
2025-08-18 22:51:33.654 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到1个需要生成文本的会议
2025-08-18 22:51:33.655 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始处理会议妙计视频转文字，会议ID：7，妙计链接：https://meetings.feishu.cn/minutes/obcnc18nhem6c5txii21x153
2025-08-18 22:51:33.687 [RMI TCP Connection(6)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 22:51:33.688 [RMI TCP Connection(6)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-18 22:51:33.690 [RMI TCP Connection(6)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-18 22:51:33.755 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Request: {"needSpeaker":null,"needTimestamp":null,"fileFormat":null,"minuteToken":"obcnc18nhem6c5txii21x153"}
2025-08-18 22:51:34.676 [scheduling-1] INFO  cn.july.feishu.util.FeishuInvokeUtil - Feishu Response data: {}, took 919 ms.
2025-08-18 22:51:34.771 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 新增会议纪要，会议ID：7
2025-08-18 22:51:34.771 [scheduling-1] INFO  c.j.o.m.s.NewMeetingDomainService - 会议纪要保存成功，会议ID：7
2025-08-18 22:51:34.771 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 会议妙计视频转文字处理成功，会议ID：7
2025-08-18 22:51:34.771 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了1个会议
2025-08-18 22:51:34.771 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 开始执行会议实际信息补充定时任务
2025-08-18 22:51:34.800 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 找到0个需要补充实际信息的会议
2025-08-18 22:51:34.800 [scheduling-1] INFO  c.j.o.m.j.MeetingActualInfoSupplementTask - 会议实际信息补充定时任务执行完成，成功：0个，失败：0个
2025-08-18 22:51:34.800 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 22:51:34.823 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 22:51:34.823 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:51:34.823 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:51:34.955 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:51:34.955 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 开始处理超期任务
2025-08-18 22:51:34.979 [scheduling-1] INFO  c.j.o.m.service.TaskActionService - 处理超期任务
2025-08-18 22:51:35.052 [scheduling-1] INFO  c.j.o.m.job.TaskOverdueProcessTask - 处理超期任务完成
2025-08-18 22:52:33.487 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:52:33.489 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:52:33.681 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:53:33.483 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:53:33.483 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:53:33.621 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:54:33.484 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:54:33.485 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:54:33.627 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:55:33.489 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:55:33.491 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:55:33.807 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:56:33.485 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:56:33.486 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:56:33.769 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:57:33.491 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:57:33.493 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:57:33.647 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:58:33.495 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:58:33.497 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:58:33.778 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 22:59:33.499 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 22:59:33.501 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 22:59:33.792 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:00:00.027 [scheduling-1] INFO  c.j.o.m.j.MeetingPlanStatusUpdateTask - 开始执行会议规划逾期状态更新任务
2025-08-18 23:00:00.075 [scheduling-1] INFO  c.j.o.m.j.MeetingPlanStatusUpdateTask - 会议规划逾期状态更新任务执行完成
2025-08-18 23:00:00.076 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 23:00:00.821 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 23:00:00.859 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 23:00:00.905 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 23:00:00.945 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 23:00:00.985 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 23:00:01.032 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 23:00:01.078 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 23:00:01.127 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 23:00:01.172 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 23:00:01.212 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 23:00:01.258 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 23:00:01.297 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 23:00:01.338 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 23:00:01.382 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 23:00:01.382 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s
2025-08-18 23:00:33.500 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:00:33.501 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:00:33.639 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:01:33.499 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 开始执行妙计视频转文字定时任务
2025-08-18 23:01:33.580 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 找到0个需要生成文本的会议
2025-08-18 23:01:33.580 [scheduling-1] INFO  c.j.o.m.job.MinuteTextGenerationTask - 妙计视频转文字定时任务执行完成，处理了0个会议
2025-08-18 23:01:33.580 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 开始检查飞书会议状态，处理遗漏的会议结束
2025-08-18 23:01:33.601 [scheduling-1] INFO  c.j.o.m.job.MeetingEndDetectionTask - 飞书会议状态检查完成，检查了0个进行中的会议
2025-08-18 23:01:33.601 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:01:33.601 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:01:33.727 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:02:33.503 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:02:33.507 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:02:33.825 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:03:33.502 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:03:33.507 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:03:33.666 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:04:33.499 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:04:33.501 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:04:33.792 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:05:33.498 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:05:33.501 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:05:33.811 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:06:33.504 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:06:33.506 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:06:33.640 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:07:33.502 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:07:33.504 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:07:33.639 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:08:33.500 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:08:33.502 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:08:33.636 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:09:33.503 [scheduling-1] INFO  c.j.o.m.job.MeetingNotificationTask - 开始执行会议提前通知定时任务
2025-08-18 23:09:33.505 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 开始执行会议提前通知任务
2025-08-18 23:09:33.660 [scheduling-1] INFO  c.j.o.m.s.MeetingNotificationActionService - 会议提前通知任务执行完成
2025-08-18 23:10:00.012 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:刷新user_token开始执行
2025-08-18 23:10:00.710 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:Nzg3MDEzYmU2NTBhNGU2NmFmYWFiYzlmNTg2YzQzNjI无须刷新
2025-08-18 23:10:00.749 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ODAyNThiYmFhY2Q5NGM2OTg5ODZlNTcyNGI3MDZhYWY无须刷新
2025-08-18 23:10:00.791 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWU0ZDhkYzZmNGQ5NDk1YWIzMzEzODM2NGI3NWM0Yjg无须刷新
2025-08-18 23:10:00.830 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDY1Y2Q1M2Q0OWI5NDhkZTg4ODIzZDdkMWY3YjMxMmM无须刷新
2025-08-18 23:10:00.873 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:ZTA3OWY4NzkzMTkzNDgzMmE0MzIxZjI5MTAyMzc1YTg无须刷新
2025-08-18 23:10:00.919 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YWRmMjcxNzM1NDljNGMwODg1ZDc2MzQ0YWE2OWE0NjM无须刷新
2025-08-18 23:10:00.962 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDhkMWQ5ZDNkMjBmNDEyNmE1ZDMxNjllYzVhYWNjZTI无须刷新
2025-08-18 23:10:01.003 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NDQzZDkyOGMxYzQwNDcyOGI3Mzc0OGZiY2U3N2IwYWM无须刷新
2025-08-18 23:10:01.042 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTg1ZDQ4NzhhZTBlNDZjODk4ZjQxMmMxMTJkZjIwNGU无须刷新
2025-08-18 23:10:01.081 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OTgyY2Y3NmZiN2UyNDA3ZGE2MGNjMDVkNzNjM2I2NWE无须刷新
2025-08-18 23:10:01.121 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:YmJmNjE5MGVlMDYwNGRlM2JiMWExN2UzY2M0NjVjYTQ无须刷新
2025-08-18 23:10:01.160 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:OWQzYTc0NWNiMjU5NGY1Yjk1YThjNTNmMWUwZjEyZDE无须刷新
2025-08-18 23:10:01.200 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NWM4YWVlNDZlNGQ2NGY2ZGJhZjUyZGU5MTFkODI1Nzk无须刷新
2025-08-18 23:10:01.239 [scheduling-1] INFO  c.j.o.m.service.SsoActionService - token:NTkyOTVmMDU3ZGY2NGQ3NWI1N2QyYzAzZjljYjI5OWE无须刷新
2025-08-18 23:10:01.239 [scheduling-1] INFO  c.j.o.m.job.RefreshUserTokenTask - 定时任务:执行耗时:1s

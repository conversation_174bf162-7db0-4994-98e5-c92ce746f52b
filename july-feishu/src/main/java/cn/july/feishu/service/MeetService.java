package cn.july.feishu.service;

import cn.july.feishu.config.AppConfig;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.model.GetMeetingModel;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.minutes.v1.model.GetMinuteTranscriptReq;
import com.lark.oapi.service.vc.v1.enums.GetMeetingUserIdTypeEnum;
import com.lark.oapi.service.vc.v1.model.GetMeetingReq;
import com.lark.oapi.service.vc.v1.model.GetMeetingRespBody;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * 飞书视频会议服务
 * @date 2025-01-24
 */
@Slf4j
public class MeetService {

    private final Client feishuClient;

    public MeetService(AppConfig appConfig) {
        feishuClient = appConfig.getFeishuClient();
    }

    public GetMeetingRespBody getMeeting(GetMeetingModel getMeetingModel) {
        GetMeetingReq getMeetingReq = GetMeetingReq.newBuilder()
                .meetingId(getMeetingModel.getMeetingId())
                .withParticipants(getMeetingModel.getWithParticipants())
                .userIdType(GetMeetingUserIdTypeEnum.OPEN_ID)
                .build();
        return FeishuInvokeUtil.executeRequest(getMeetingReq, feishuClient.vc().meeting()::get, FeishuErrorCode.MEETING_GET_FAIL);
    }

    public ByteArrayOutputStream getMinuteText(String userAccessToken, String minuteUrl) {
        String minuteToken = extractMinuteTokenFromUrl(minuteUrl);
        GetMinuteTranscriptReq req = GetMinuteTranscriptReq.newBuilder()
                .minuteToken(minuteToken)
                .build();
        RequestOptions requestOptions = RequestOptions.newBuilder().userAccessToken(userAccessToken).build();
        return FeishuInvokeUtil.executeRequest(req,requestOptions, feishuClient.minutes().v1().minuteTranscript()::get, FeishuErrorCode.MINUTE_GET_FAIL);
    }

    private String extractMinuteTokenFromUrl(String minuteUrl) {
        // 使用 "/" 分割 URL，取最后一部分作为 token
        String[] parts = minuteUrl.split("/");
        return parts[parts.length - 1];
    }
}

package cn.july.feishu.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 获取会议对象
 * @date 2024-12-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetMeetingModel {

    /**
     * 是否需要参会人列表
     * <p> 示例值：false
     */
    private Boolean withParticipants;
    /**
     * 此次调用中使用的用户ID的类型，默认使用open_id可不填
     * <p> 示例值：
     */
    private String userIdType;
    /**
     * 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
     * <p> 示例值：6911188411932033028
     */
    private String meetingId;
}

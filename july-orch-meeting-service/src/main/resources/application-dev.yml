knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://************:6379",
            "password": "July123456.",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
#dromara:
#  x-file-storage: #文件存储配置
#    default-platform: minio-1 #默认使用的存储平台
#    thumbnail-suffix: ".min.jpg"
#    minio:
#      - platform: minio-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: FHZArYPFxUOjadtHNGbo
#        secret-key: iSjOzz2RB1ibZ2jV4WpOi2Tolp7kTZNfPDfB18rg
#        end-point: http://coe-file-sit.pengfeijituan.com:9000/
#        bucket-name: meeting-1
#        domain: http://coe-file-sit.pengfeijituan.com:9000/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
#        base-path: ${spring.application.name}/
dromara:
  x-file-storage: #文件存储配置
    default-platform: huawei-obs-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    huawei-obs:
      - platform: huawei-obs-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: ERMZYOVKRBTVCAIBTIJ2
        secret-key: phJPIFwjjdnFnjWCOG5mihaMhLBsMUnlneQED4mE
        end-point: obs.cn-north-4.myhuaweicloud.com
        bucket-name: test-kangjian
        domain: https://test-kangjian.obs.cn-north-4.myhuaweicloud.com:443/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
        base-path: genn-pf-orch-meeting
feishu:
  mini:
    appId: cli_a80a244ad965d00d
    appSecret: iHiwU7PYLQeN8oCH8ku4OB7ItEa0Vh8T
july:
  meeting:
    cardSend:
      jumpUrl:
        evaluationUrl: http://www.baidu.com

    agent:
      invokeDomain: https://cerebro-sit.genn.cn
    permission:
      excludePatterns:
        - /user/getUserInfo
        - /file/**
        - /app/**
        - /analysis-report/**
        - /assistant/**
        - /test/**
  database:
    multi:
      db:
        july_orch_meeting:
          primary: true
          master:
            jdbcUrl: ***********************************************************************************************************************************************************************************
            username: root
            password: July123456.
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
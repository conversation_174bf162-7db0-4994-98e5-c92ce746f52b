-- 创建会议纪要表
CREATE TABLE meeting_minute (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    meeting_id BIGINT NOT NULL COMMENT '会议ID，关联new_meeting表',
    minute_text LONGTEXT COMMENT '会议纪要文本内容',
    minute_text_length INT COMMENT '文本长度（字符数）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_user_id VARCHAR(100) COMMENT '创建人ID',
    create_user_name VARCHAR(100) COMMENT '创建人姓名',
    
    UNIQUE KEY uk_meeting_id (meeting_id),
    INDEX idx_create_time (create_time)
) COMMENT '会议纪要表';

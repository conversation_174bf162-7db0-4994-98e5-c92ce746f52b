<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.NewMeetingMapper">
    <resultMap id="NewMeetingListDTOResultMap" type="cn.july.orch.meeting.domain.dto.NewMeetingListDTO">
        <id column="id" property="id"/>
        <result column="meeting_name" property="meetingName"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="priority_level" property="priorityLevel"/>
        <result column="meeting_location" property="meetingLocation"/>
        <result column="attendees" property="attendees" typeHandler="cn.july.database.mybatisplus.typehandler.ListStringTypeHandler"/>
        <result column="attendee_count" property="attendeeCount"/>
        <result column="host_user_id" property="hostUserId"/>
        <result column="recorder_user_id" property="recorderUserId"/>
        <result column="meeting_url" property="meetingUrl"/>
        <result column="minute_url" property="minuteUrl"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="pre_meeting_documents" property="preMeetingDocuments" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="actual_start_time" property="actualStartTime"/>
        <result column="actual_end_time" property="actualEndTime"/>
        <result column="actual_attendees" property="actualAttendees" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="report_status" property="reportStatus"/>
    </resultMap>

    <select id="queryPage" resultMap="NewMeetingListDTOResultMap">
        SELECT
            nm.id,
            nm.meeting_name,
            nm.start_time,
            nm.end_time,
            nm.status,
            nm.priority_level,
            nm.meeting_location,
            nm.attendees,
            JSON_LENGTH(nm.attendees) as attendee_count,
            nm.host_user_id,
            nm.recorder_user_id,
            nm.meeting_url,
            nm.minute_url,
            nm.create_user_name,
            nm.create_time,
            nm.pre_meeting_documents,
            nm.actual_start_time,
            nm.actual_end_time,
            nm.actual_attendees,
            mar.status as report_status
        FROM new_meeting nm
        LEFT JOIN meeting_analysis_reports mar ON nm.id = mar.meeting_id
        <where>
            <!-- 逻辑删除条件：只查询未删除的数据 -->
            AND nm.deleted = 0
            <if test="query.meetingName != null and query.meetingName != ''">
                AND nm.meeting_name LIKE CONCAT('%', #{query.meetingName}, '%')
            </if>
            <if test="query.status != null">
                AND nm.status = #{query.status}
            </if>
            <if test="query.priorityLevel != null">
                AND nm.priority_level = #{query.priorityLevel}
            </if>
            <if test="query.meetingPlanId != null">
                AND nm.meeting_plan_id = #{query.meetingPlanId}
            </if>
            <if test="query.meetingStandardId != null">
                AND nm.meeting_standard_id = #{query.meetingStandardId}
            </if>
            <if test="query.startTimeFrom != null">
                AND nm.start_time &gt;= #{query.startTimeFrom}
            </if>
            <if test="query.startTimeTo != null">
                AND nm.start_time &lt;= #{query.startTimeTo}
            </if>
            <if test="query.createUserId != null and query.createUserId != ''">
                AND nm.create_user_id = #{query.createUserId}
            </if>
        </where>
        ORDER BY nm.create_time DESC
    </select>
</mapper>

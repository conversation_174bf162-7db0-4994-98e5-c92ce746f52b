<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingEvaluationMapper">

    <!-- 根据会议ID查询评价统计信息 -->
    <select id="selectByMeetingId" resultType="cn.july.orch.meeting.domain.po.MeetingEvaluationPO">
        SELECT *
        FROM meeting_evaluation
        WHERE meeting_id = #{meetingId}
          AND deleted = 0
        ORDER BY evaluation_time DESC
    </select>

    <!-- 根据会议ID查询评价条数 -->
    <select id="countByMeetingId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM meeting_evaluation
        WHERE meeting_id = #{meetingId}
          AND deleted = 0
    </select>

    <!-- 根据会议ID查询各项评分的平均分 -->
    <select id="selectAverageScoresByMeetingId" resultType="java.lang.Double">
        SELECT 
            AVG(meeting_score) as avg_meeting_score,
            AVG(content_score) as avg_content_score,
            AVG(duration_score) as avg_duration_score,
            AVG(effectiveness_score) as avg_effectiveness_score
        FROM meeting_evaluation
        WHERE meeting_id = #{meetingId}
          AND deleted = 0
    </select>

    <!-- 根据会议ID查询改进建议列表 -->
    <select id="selectSuggestionsByMeetingId" resultType="java.lang.String">
        SELECT suggestions
        FROM meeting_evaluation
        WHERE meeting_id = #{meetingId}
          AND deleted = 0
          AND suggestions IS NOT NULL
          AND suggestions != ''
        ORDER BY evaluation_time DESC
    </select>

    <select id="selectByMeetingIdAndEvaluator" resultType="cn.july.orch.meeting.domain.po.MeetingEvaluationPO">
        SELECT *
        FROM meeting_evaluation
        WHERE meeting_id = #{meetingId}
          AND evaluator_open_id = #{evaluatorOpenId}
          AND deleted = 0
        ORDER BY evaluation_time DESC
    </select>
</mapper>
package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务优先级枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum TaskPriorityEnum {

    /**
     * 低优先级
     */
    LOW(0, "低"),
    /**
     * 中优先级
     */
    MEDIUM(1, "中"),
    /**
     * 高优先级
     */
    HIGH(2, "高"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, TaskPriorityEnum> VALUES = new HashMap<>();

    static {
        for (final TaskPriorityEnum item : TaskPriorityEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TaskPriorityEnum of(int code) {
        return VALUES.get(code);
    }

    /**
     * 根据code获取枚举
     */
    public static TaskPriorityEnum getByCode(Integer code) {
        return VALUES.get(code);
    }
}

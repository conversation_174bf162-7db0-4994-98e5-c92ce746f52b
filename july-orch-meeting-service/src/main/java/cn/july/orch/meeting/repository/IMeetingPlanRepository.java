package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.domain.entity.MeetingPlan;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划仓储接口
 * @date 2025-01-24
 */
public interface IMeetingPlanRepository {

    /**
     * 保存会议规划
     */
    void save(MeetingPlan meetingPlan);

    /**
     * 更新会议规划
     */
    void update(MeetingPlan meetingPlan);

    /**
     * 根据ID查询会议规划
     */
    MeetingPlan findById(Long id);

    /**
     * 删除会议规划
     */
    void deleteById(Long id);

    /**
     * 检查时间段内是否有冲突的会议规划
     */
    List<MeetingPlan> findConflictPlans(LocalDateTime startTime, LocalDateTime endTime, 
                                       String location, List<String> attendees, Long excludeId);

    /**
     * 查询即将开始的会议规划（用于提醒）
     */
    List<MeetingPlan> findUpcomingPlans(int advanceNoticeDays);

    /**
     * 查询已逾期的会议规划
     */
    List<MeetingPlan> findOverduePlans();

    /**
     * 查询需要发送通知的会议规划
     */
    List<MeetingPlan> findPlansForNotification(Long standardId, LocalDateTime startTime, LocalDateTime endTime, int noticeMinutes);

    /**
     * 更新提前通知发送标记
     */
    void updateAdvanceNoticeSent(Long planId, Integer sent);
}

package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.database.mybatisplus.typehandler.ListStringTypeHandler;
import cn.july.orch.meeting.config.MeetingRoleListTypeHandler;
import cn.july.orch.meeting.enums.MeetingRoleEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准持久化对象
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting_standard", autoResultMap = true)
public class MeetingStandardPO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 标准名称
     */
    @TableField("standard_name")
    private String standardName;

    /**
     * 标准描述
     */
    @TableField("description")
    private String description;

    /**
     * 默认持续时长(分钟)
     */
    @TableField("default_duration")
    private Integer defaultDuration;

    /**
     * 默认提前通知数值
     */
    @TableField("advance_notice_value")
    private Integer advanceNoticeValue;

    /**
     * 默认提前通知时间单位
     */
    @TableField("advance_notice_unit")
    private TimeUnitEnum advanceNoticeUnit;

    /**
     * 默认优先级
     */
    @TableField("priority_level")
    private PriorityLevelEnum priorityLevel;

    /**
     * 默认会议地点
     */
    @TableField("default_location")
    private String defaultLocation;

    /**
     * 迟到容许时间(分钟)
     */
    @TableField("late_tolerance_minutes")
    private Integer lateToleranceMinutes;

    /**
     * 人员必要角色列表
     */
    @TableField(value = "required_roles", typeHandler = MeetingRoleListTypeHandler.class)
    private List<MeetingRoleEnum> requiredRoles;

    /**
     * 最少参会人数
     */
    @TableField("min_attendees")
    private Integer minAttendees;

    /**
     * 最多参会人数
     */
    @TableField("max_attendees")
    private Integer maxAttendees;

    /**
     * 会议要点列表
     */
    @TableField(value = "meeting_points", typeHandler = ListStringTypeHandler.class)
    private List<String> meetingPoints;

    /**
     * 是否启用(0-否,1-是)
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记(0-未删除,1-已删除)
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;
}

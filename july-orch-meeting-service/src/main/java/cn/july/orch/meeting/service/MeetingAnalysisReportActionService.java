package cn.july.orch.meeting.service;

import cn.july.orch.meeting.common.AgentConstants;
import cn.july.orch.meeting.domain.command.GenerateAnalysisReportCommand;
import cn.july.orch.meeting.domain.command.MeetingAnalysisReportCommand;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.entity.MeetingAnalysisReport;
import cn.july.orch.meeting.domain.response.AgentCompleteRespDTO;
import cn.july.orch.meeting.enums.ReportStatusEnum;
import cn.july.orch.meeting.repository.IMeetingAnalysisReportRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 会议分析报告Action服务
 */
@Slf4j
@Service
public class MeetingAnalysisReportActionService {

    @Resource
    private IMeetingAnalysisReportRepository meetingAnalysisReportRepository;

    @Resource
    private NewMeetingQueryService meetingRepository;

    @Resource
    private AgentInvokeService agentInvokeService;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 会议分析智能体应用ID
     */
    private static final String MEETING_ANALYSIS_APP_ID = "6892ae7c9e9b7bc7598f5c54";

    /**
     * 创建会议分析报告
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createReport(MeetingAnalysisReportCommand command) {
        log.info("创建会议分析报告，会议ID：{}", command.getMeetingId());

        // 检查是否已存在报告，如果存在则更新状态为生成中
        MeetingAnalysisReport existingReport = meetingAnalysisReportRepository.findByMeetingId(command.getMeetingId());
        if (existingReport != null) {
            log.info("会议已存在分析报告，更新状态为生成中，会议ID：{}", existingReport.getId());
            existingReport.setStatus(ReportStatusEnum.GENERATED);
            existingReport.setOverallScore(command.getOverallScore());
            existingReport.setOverallSummary(command.getOverallSummary());
            existingReport.setContentAnalysisJson(command.getContentAnalysisJson());
            existingReport.setAiSuggestionsJson(command.getAiSuggestionsJson());
            meetingAnalysisReportRepository.update(existingReport);
            return existingReport.getId();
        }

        // 创建新报告，状态设置为生成中
        MeetingAnalysisReport report = MeetingAnalysisReport.builder()
                .meetingId(command.getMeetingId())
                .status(ReportStatusEnum.GENERATED)
                .overallScore(command.getOverallScore())
                .overallSummary(command.getOverallSummary())
                .contentAnalysisJson(command.getContentAnalysisJson())
                .aiSuggestionsJson(command.getAiSuggestionsJson())
                .build();

        meetingAnalysisReportRepository.save(report);
        log.info("会议分析报告创建成功，报告ID：{}", report.getId());

        return report.getId();
    }

    /**
     * 更新会议分析报告
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReport(Long id, MeetingAnalysisReportCommand command) {
        log.info("更新会议分析报告，报告ID：{}", id);

        // 查询现有报告
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findById(id);
        if (report == null) {
            log.warn("会议分析报告不存在，报告ID：{}", id);
            throw new IllegalArgumentException("会议分析报告不存在");
        }

        // 更新报告内容
        report.setOverallScore(command.getOverallScore());
        report.setOverallSummary(command.getOverallSummary());
        report.setContentAnalysisJson(command.getContentAnalysisJson());
        report.setAiSuggestionsJson(command.getAiSuggestionsJson());

        meetingAnalysisReportRepository.update(report);
        log.info("会议分析报告更新成功，报告ID：{}", id);
    }

    /**
     * 删除会议分析报告
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteReport(Long id) {
        log.info("删除会议分析报告，报告ID：{}", id);

        // 查询现有报告
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findById(id);
        if (report == null) {
            log.warn("会议分析报告不存在，报告ID：{}", id);
            throw new IllegalArgumentException("会议分析报告不存在");
        }

        meetingAnalysisReportRepository.deleteById(id);
        log.info("会议分析报告删除成功，报告ID：{}", id);
    }

    /**
     * 初始化报告记录（同步方法）
     */
    @Transactional(rollbackFor = Exception.class)
    public Long initReport(Long meetingId) {
        // 检查会议是否存在
        NewMeetingDTO meetingAgg = meetingRepository.getById(meetingId);
        if (meetingAgg == null) {
            log.warn("会议不存在，会议ID：{}", meetingId);
            throw new IllegalArgumentException("会议不存在");
        }

        // 创建或更新报告状态为生成中
        MeetingAnalysisReport report = meetingAnalysisReportRepository.findByMeetingId(meetingId);
        if (report == null) {
            report = MeetingAnalysisReport.builder()
                    .meetingId(meetingId)
                    .status(ReportStatusEnum.GENERATING)
                    .build();
            meetingAnalysisReportRepository.save(report);
            log.info("创建会议分析报告记录，状态为生成中，会议ID：{}", meetingId);
        } else {
            report.setStatus(ReportStatusEnum.GENERATING);
            meetingAnalysisReportRepository.update(report);
            log.info("更新会议分析报告状态为生成中，报告ID：{}", report.getId());
        }

        return report.getId();
    }

    /**
     * 生成会议分析报告（异步方法）
     */
    @Async("reportExecutor")
    public void generateReport(GenerateAnalysisReportCommand command) {
        log.info("开始异步生成会议分析报告，会议ID：{}", command.getMeetingId());
        Long meetingId = command.getMeetingId();

        try {
            // 构建提示词
            String prompt = "请对这个会议进行全面分析。分析内容包括：\n" +
                "1. 会议整体评分（0-100分）和总结\n" +
                "2. 从多个维度（如目标明确度、讨论效率、决策达成度等）进行评分和分析\n" +
                "3. 提供具体的优化建议";

            // 构建variables参数
            Map<String, Object> variables = new HashMap<>();
            variables.put("meetingId", meetingId);

            // 调用智能体生成分析报告
            AgentCompleteRespDTO response = agentInvokeService.invokeStreamGetAnswer(
                MEETING_ANALYSIS_APP_ID,
                prompt,
                variables,
                AgentConstants.REPORT_AUTHORIZATION
            );

            if (response == null || response.getAnswer() == null) {
                throw new RuntimeException("生成分析报告失败：智能体返回结果为空");
            }

            // 更新报告内容和状态
            MeetingAnalysisReport report = meetingAnalysisReportRepository.findByMeetingId(meetingId);
            if (report != null) {
                report.setStatus(ReportStatusEnum.GENERATED);
                report.setOverallSummary(response.getAnswer());
                meetingAnalysisReportRepository.update(report);
                log.info("会议分析报告生成完成，报告ID：{}", report.getId());
            }

        } catch (Exception e) {
            log.error("生成会议分析报告失败，会议ID：{}", meetingId, e);
            throw new RuntimeException("生成会议分析报告失败：" + e.getMessage());
        }
    }
}


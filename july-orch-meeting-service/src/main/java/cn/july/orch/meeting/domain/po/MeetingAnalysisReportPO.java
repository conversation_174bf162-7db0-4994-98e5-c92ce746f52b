package cn.july.orch.meeting.domain.po;

import cn.july.orch.meeting.enums.ReportStatusEnum;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * MeetingAnalysisReportPO对象
 *
 * <AUTHOR> Assistant
 * @desc 会议分析报告表
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting_analysis_reports", autoResultMap = true)
public class MeetingAnalysisReportPO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 关联的会议ID
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 状态（0-生成中 1-已生成）
     */
    @TableField("status")
    private ReportStatusEnum status;

    /**
     * 综合评分 (0-100)
     */
    @TableField("overall_score")
    private Integer overallScore;

    /**
     * 综合评分的描述文字
     */
    @TableField("overall_summary")
    private String overallSummary;

    /**
     * 包含内容质量各项评分和AI分析摘要的JSON对象
     */
    @TableField("content_analysis_json")
    private String contentAnalysisJson;

    /**
     * 包含所有AI优化建议的JSON数组
     */
    @TableField("ai_suggestions_json")
    private String aiSuggestionsJson;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
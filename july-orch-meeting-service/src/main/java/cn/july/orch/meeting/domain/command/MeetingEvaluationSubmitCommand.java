package cn.july.orch.meeting.domain.command;

import cn.july.orch.meeting.domain.dto.MeetingEvaluationDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 会议评价提交命令
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEvaluationSubmitCommand {
    
    private Long meetingId;
    private String evaluatorOpenId;
    private String evaluatorName;
    private MeetingEvaluationDTO evaluationDTO;
} 
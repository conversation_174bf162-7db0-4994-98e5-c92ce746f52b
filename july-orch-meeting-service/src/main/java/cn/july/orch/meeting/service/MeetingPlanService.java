package cn.july.orch.meeting.service;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.MeetingPlanAssembler;
import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.dto.*;
import cn.july.orch.meeting.domain.entity.MeetingPlan;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.domain.query.MeetingPlanQuery;
import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.mapper.MeetingPlanMapper;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议规划服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingPlanService {

    private final MeetingPlanMapper meetingPlanMapper;
    private final MeetingStandardMapper meetingStandardMapper;
    private final MeetingPlanAssembler meetingPlanAssembler;
    private final UserInfoQueryService userInfoQueryService;

    /**
     * 创建会议规划
     */
    @Transactional
    public void createMeetingPlan(MeetingPlanCreateCommand command) {
        // 1. 验证会议标准
        MeetingStandardPO standard = meetingStandardMapper.selectById(command.getMeetingStandardId());
        if (standard == null) {
            throw new RuntimeException("会议标准不存在");
        }

        // 2. 验证会议开始时间必须在当前时间之后
        if (command.getPlannedStartTime().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("会议开始时间必须在当前时间之后");
        }

        // 3. 应用默认配置
        MeetingPlan meetingPlan = meetingPlanAssembler.toEntity(command);
        applyStandardDefaults(meetingPlan, standard);

        // 4. 处理重复规则
        if (command.getRecurrenceRule() != null) {
            meetingPlan.setIsRecurring(IsRecurringEnum.YES);
            setRecurrenceFields(meetingPlan, command.getRecurrenceRule());
        } else {
            meetingPlan.setIsRecurring(IsRecurringEnum.NO);
        }

        // 5. 计算持续时长
        meetingPlan.calculateDuration();

        // 5. 校验会议规划是否符合会议标准限制
        validateMeetingPlanAgainstStandard(meetingPlan, standard);

        // 6. 设置初始状态
        meetingPlan.setStatus(MeetingPlanStatusEnum.NOT_STARTED);

        // 7. 设置提前通知发送标记为未发送
        meetingPlan.setAdvanceNoticeSent(0);

        // 8. 冲突检测
        validateNoConflicts(meetingPlan);

        // 9. 保存
        MeetingPlanPO po = meetingPlanAssembler.toPO(meetingPlan);
        meetingPlanMapper.insert(po);
    }

    /**
     * 更新会议规划
     */
    @Transactional
    public void updateMeetingPlan(MeetingPlanUpdateCommand command) {
        // 1. 检查是否存在
        MeetingPlan existing = meetingPlanAssembler.toEntity(meetingPlanMapper.selectById(command.getId()));
        if (existing == null) {
            throw new RuntimeException("会议规划不存在");
        }

        // 2. 验证会议标准
        MeetingStandardPO standard = meetingStandardMapper.selectById(command.getMeetingStandardId());
        if (standard == null) {
            throw new RuntimeException("会议标准不存在");
        }

        // 3. 验证会议开始时间必须在当前时间之后
        if (command.getPlannedStartTime().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("会议开始时间必须在当前时间之后");
        }

        // 4. 应用更新
        MeetingPlan meetingPlan = meetingPlanAssembler.toEntity(command);
        applyStandardDefaults(meetingPlan, standard);

        // 5. 处理重复规则更新
        if (command.getRecurrenceRule() != null) {
            meetingPlan.setIsRecurring(IsRecurringEnum.YES);
            setRecurrenceFields(meetingPlan, command.getRecurrenceRule());
        } else {
            meetingPlan.setIsRecurring(IsRecurringEnum.NO);
        }

        // 6. 计算持续时长
        meetingPlan.calculateDuration();

        // 6. 校验会议规划是否符合会议标准限制
        validateMeetingPlanAgainstStandard(meetingPlan, standard);

        // 7. 冲突检测（排除自身）
        validateNoConflicts(meetingPlan);

        // 8. 更新
        MeetingPlanPO po = meetingPlanAssembler.toPO(meetingPlan);
        meetingPlanMapper.updateById(po);
    }

    /**
     * 删除会议规划
     */
    @Transactional
    public void deleteMeetingPlan(Long id) {
        // 1. 检查是否存在
        MeetingPlan existing = meetingPlanAssembler.toEntity(meetingPlanMapper.selectById(id));
        if (existing == null) {
            throw new RuntimeException("会议规划不存在");
        }

        // 2. 检查是否可以删除（只有未开始的会议规划可以删除）
        if (existing.getStatus() != MeetingPlanStatusEnum.NOT_STARTED) {
            throw new RuntimeException("只有未开始的会议规划可以删除");
        }

        // 3. 删除
        meetingPlanMapper.deleteById(id);
    }

    /**
     * 分页查询会议规划列表
     */
    public PageResultDTO<MeetingPlanListDTO> queryPage(MeetingPlanQuery query) {
        Page<MeetingPlanPO> page = new Page<>(query.getPageNo(), query.getPageSize());
        Page<MeetingPlanListDTO> resultPage = meetingPlanMapper.queryPage(page, query);

        // 填充参会人员详细信息
        fillAttendeeDetails(resultPage.getRecords());

        return meetingPlanAssembler.toPageResult(resultPage);
    }

    /**
     * 根据ID查询会议规划详情
     */
    public MeetingPlanDTO getById(Long id) {
        MeetingPlanPO po = meetingPlanMapper.selectById(id);
        if (po == null) {
            return null;
        }
        MeetingPlan entity = meetingPlanAssembler.toEntity(po);
        return meetingPlanAssembler.toDTO(entity);
    }

    /**
     * 查询日历维度的会议规划
     */
    public List<MeetingPlanCalendarDTO> queryCalendar(MeetingPlanCalendarQuery query) {
        return meetingPlanMapper.queryCalendar(query);
    }

    /**
     * 检查会议规划冲突
     */
    public List<ConflictInfo> checkConflicts(LocalDateTime startTime, LocalDateTime endTime,
                                           String location, List<String> attendees, Long excludeId) {
        List<MeetingPlan> conflicts = findConflictPlans(startTime, endTime, location, attendees, excludeId);

        return conflicts.stream()
            .map(conflict -> buildConflictInfo(conflict, location, attendees))
            .collect(Collectors.toList());
    }

    /**
     * 查找冲突的会议规划
     */
    private List<MeetingPlan> findConflictPlans(LocalDateTime startTime, LocalDateTime endTime,
                                               String location, List<String> attendees, Long excludeId) {
        LambdaQueryWrapper<MeetingPlanPO> wrapper = new LambdaQueryWrapper<>();
        
        // 时间冲突检查
        wrapper.and(w -> w
            .and(ww -> ww.le(MeetingPlanPO::getPlannedStartTime, startTime).ge(MeetingPlanPO::getPlannedEndTime, startTime))
            .or(ww -> ww.le(MeetingPlanPO::getPlannedStartTime, endTime).ge(MeetingPlanPO::getPlannedEndTime, endTime))
            .or(ww -> ww.ge(MeetingPlanPO::getPlannedStartTime, startTime).le(MeetingPlanPO::getPlannedEndTime, endTime))
        );
        
        if (excludeId != null) {
            wrapper.ne(MeetingPlanPO::getId, excludeId);
        }
        
        List<MeetingPlanPO> pos = meetingPlanMapper.selectList(wrapper);
        
        return pos.stream()
            .map(meetingPlanAssembler::toEntity)
            .filter(plan -> hasConflict(plan, location, attendees))
            .collect(Collectors.toList());
    }

    /**
     * 检查是否有冲突
     */
    private boolean hasConflict(MeetingPlan plan, String newLocation, List<String> newAttendees) {
        // 检查地点冲突
        if (StringUtils.hasText(newLocation) && StringUtils.hasText(plan.getMeetingLocation())) {
            if (newLocation.equals(plan.getMeetingLocation())) {
                return true;
            }
        }
        
        // 检查人员冲突
        if (newAttendees != null && plan.getAttendees() != null) {
            for (String attendee : newAttendees) {
                if (plan.getAttendees().contains(attendee)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 构建冲突信息
     */
    private ConflictInfo buildConflictInfo(MeetingPlan conflict, String newLocation, List<String> newAttendees) {
        boolean locationConflict = hasLocationConflict(newLocation, conflict.getMeetingLocation());
        boolean attendeeConflict = hasAttendeeConflict(newAttendees, conflict.getAttendees());

        List<String> conflictAttendees = null;
        if (attendeeConflict && newAttendees != null && conflict.getAttendees() != null) {
            conflictAttendees = newAttendees.stream()
                .filter(attendee -> conflict.getAttendees().contains(attendee))
                .collect(Collectors.toList());
        }

        String message = buildConflictMessage(conflict, locationConflict, attendeeConflict);

        return ConflictInfo.builder()
            .conflictPlanId(conflict.getId())
            .conflictPlanName(conflict.getPlanName())
            .conflictStartTime(conflict.getPlannedStartTime())
            .conflictEndTime(conflict.getPlannedEndTime())
            .hasLocationConflict(locationConflict)
            .conflictLocation(conflict.getMeetingLocation())
            .hasAttendeeConflict(attendeeConflict)
            .conflictAttendees(conflictAttendees)
            .conflictMessage(message)
            .build();
    }

    /**
     * 检查地点冲突
     */
    private boolean hasLocationConflict(String location1, String location2) {
        return StringUtils.hasText(location1) && StringUtils.hasText(location2) && location1.equals(location2);
    }

    /**
     * 检查人员冲突
     */
    private boolean hasAttendeeConflict(List<String> attendees1, List<String> attendees2) {
        if (attendees1 == null || attendees2 == null) {
            return false;
        }
        return attendees1.stream().anyMatch(attendees2::contains);
    }

    /**
     * 构建冲突消息
     */
    private String buildConflictMessage(MeetingPlan conflict, boolean locationConflict, boolean attendeeConflict) {
        StringBuilder message = new StringBuilder();
        message.append("与会议规划「").append(conflict.getPlanName()).append("」存在冲突：");
        
        if (locationConflict) {
            message.append("会议室冲突");
        }
        if (locationConflict && attendeeConflict) {
            message.append("、");
        }
        if (attendeeConflict) {
            message.append("人员冲突");
        }
        
        return message.toString();
    }

    /**
     * 应用会议标准默认配置
     */
    private void applyStandardDefaults(MeetingPlan meetingPlan, MeetingStandardPO standard) {
        if (meetingPlan.getPriorityLevel() == null) {
            meetingPlan.setPriorityLevel(standard.getPriorityLevel());
        }
    }

    /**
     * 校验会议规划是否符合会议标准限制
     */
    private void validateMeetingPlanAgainstStandard(MeetingPlan meetingPlan, MeetingStandardPO standard) {
        // 检查参会人数
        if (meetingPlan.getAttendees() != null) {
            int attendeeCount = meetingPlan.getAttendees().size();
            if (standard.getMinAttendees() != null && attendeeCount < standard.getMinAttendees()) {
                throw new RuntimeException("参会人数不能少于" + standard.getMinAttendees() + "人");
            }
            if (standard.getMaxAttendees() != null && attendeeCount > standard.getMaxAttendees()) {
                throw new RuntimeException("参会人数不能超过" + standard.getMaxAttendees() + "人");
            }
        }
        
        // 检查会议时长
        if (meetingPlan.getPlannedDuration() != null && standard.getDefaultDuration() != null) {
            if (meetingPlan.getPlannedDuration() > standard.getDefaultDuration()) {
                throw new RuntimeException("会议时长不能超过" + standard.getDefaultDuration() + "分钟");
            }
        }
    }

    /**
     * 验证无冲突
     */
    private void validateNoConflicts(MeetingPlan meetingPlan) {
        List<MeetingPlan> conflicts = findConflictPlans(
            meetingPlan.getPlannedStartTime(),
            meetingPlan.getPlannedEndTime(),
            meetingPlan.getMeetingLocation(),
            meetingPlan.getAttendees(),
            meetingPlan.getId()
        );
        
        if (!conflicts.isEmpty()) {
            throw new RuntimeException("存在时间冲突的会议规划");
        }
    }

    /**
     * 获取日历背景色
     */
    private String getCalendarBackgroundColor(MeetingPlanStatusEnum status) {
        switch (status) {
            case NOT_STARTED:
                return "#1890ff";
            case IN_PROGRESS:
                return "#52c41a";
            case COMPLETED:
                return "#faad14";
            case OVERDUE:
                return "#f5222d";
            default:
                return "#d9d9d9";
        }
    }

    /**
     * 填充参会人员详细信息
     */
    private void fillAttendeeDetails(List<MeetingPlanListDTO> meetingPlans) {
        if (meetingPlans == null || meetingPlans.isEmpty()) {
            return;
        }

        // 收集所有参会人员ID
        List<String> allAttendeeIds = meetingPlans.stream()
                .filter(plan -> plan.getAttendees() != null)
                .flatMap(plan -> plan.getAttendees().stream())
                .distinct()
                .collect(Collectors.toList());

        if (allAttendeeIds.isEmpty()) {
            return;
        }

        // 批量获取用户信息
        Map<String, FSUserInfoDTO> userInfoMap = userInfoQueryService.getUserInfos(allAttendeeIds);

        // 为每个会议规划填充参会人员详细信息
        meetingPlans.forEach(plan -> {
            if (plan.getAttendees() != null && !plan.getAttendees().isEmpty()) {
                List<FSUserInfoDTO> attendeeDetails = plan.getAttendees().stream()
                        .map(userInfoMap::get)
                        .filter(userInfo -> userInfo != null)
                        .collect(Collectors.toList());
                plan.setAttendeeDetails(attendeeDetails);
            }
        });
    }

    /**
     * 设置重复规则字段
     */
    private void setRecurrenceFields(MeetingPlan meetingPlan, RecurrenceRule rule) {
        meetingPlan.setRecurrenceType(rule.getRecurrenceType());
        meetingPlan.setRecurrenceInterval(rule.getRecurrenceInterval());
        meetingPlan.setRecurrenceWeekdays(rule.getRecurrenceWeekdays() != null ?
            rule.getRecurrenceWeekdays().stream().map(String::valueOf).collect(Collectors.joining(",")) : null);
        meetingPlan.setRecurrenceMonthDay(rule.getRecurrenceMonthDay());
        meetingPlan.setRecurrenceEndDate(rule.getRecurrenceEndDate());
    }
}

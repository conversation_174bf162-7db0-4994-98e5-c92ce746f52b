package cn.july.orch.meeting.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 会议评价实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEvaluation {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议ID
     */
    private Long meetingId;

    /**
     * 评价人OpenID
     */
    private String evaluatorOpenId;

    /**
     * 评价人姓名
     */
    private String evaluatorName;

    /**
     * 会议评分(1-5)
     */
    private Integer meetingScore;

    /**
     * 内容评分(1-5)
     */
    private Integer contentScore;

    /**
     * 时长评分(1-5)
     */
    private Integer durationScore;

    /**
     * 效果评分(1-5)
     */
    private Integer effectivenessScore;

    /**
     * 改进建议
     */
    private String suggestions;

    /**
     * 评价时间
     */
    private LocalDateTime evaluationTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Integer deleted;
} 
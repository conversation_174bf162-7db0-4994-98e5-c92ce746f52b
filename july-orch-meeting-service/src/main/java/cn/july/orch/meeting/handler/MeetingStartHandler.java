package cn.july.orch.meeting.handler;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.model.callback.EventCallbackCommand;
import cn.july.orch.meeting.domain.command.MeetingStartEndEventCommand;
import cn.july.orch.meeting.enums.EventCallbackEnum;
import cn.july.orch.meeting.service.NewMeetingActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议开始回调处理器
 * @date 2024-12-25
 */
@Slf4j
@Component
public class MeetingStartHandler implements CallbackHandler<MeetingStartEndEventCommand> {

    @Resource
    private NewMeetingActionService newMeetingActionService;

    @Override
    public void handle(EventCallbackCommand command) {
        String meetingJson = JsonUtils.toJson(command.getEvent());
        MeetingStartEndEventCommand startCommand = JsonUtils.parse(meetingJson, MeetingStartEndEventCommand.class);
        MeetingStartEndEventCommand.Meeting fsCommand = startCommand.getMeeting();
        // 新会议同步
        newMeetingActionService.handleFeishuMeetingStartCallback(
            fsCommand.getCalendarEventId(),
            fsCommand.getMeetingNo(),
            fsCommand.getId(),
            fsCommand.getStartTime()
        );
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_START;
    }
}

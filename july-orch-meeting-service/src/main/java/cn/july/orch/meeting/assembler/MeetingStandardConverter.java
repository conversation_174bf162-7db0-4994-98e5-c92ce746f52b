package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.entity.MeetingStandard;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 会议标准转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingStandardConverter {

    MeetingStandardPO toPO(MeetingStandard meetingStandard);

    MeetingStandard toEntity(MeetingStandardPO meetingStandardPO);
}

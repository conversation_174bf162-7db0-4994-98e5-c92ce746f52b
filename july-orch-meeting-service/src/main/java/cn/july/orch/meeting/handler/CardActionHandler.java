package cn.july.orch.meeting.handler;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.model.callback.EventCallbackCommand;
import cn.july.orch.meeting.domain.command.CardActionEventCommand;
import cn.july.orch.meeting.domain.dto.ActionValueDTO;
import cn.july.orch.meeting.domain.dto.MeetingEvaluationDTO;
import cn.july.orch.meeting.enums.EventCallbackEnum;
import cn.july.orch.meeting.feishu.FeishuApiService;
import cn.july.orch.meeting.service.MeetingEvaluationService;
import cn.july.orch.meeting.utils.JsonUnescapeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Assistant
 * @description 卡片交互事件处理器
 */
@Slf4j
@Component
public class CardActionHandler implements CallbackHandler<CardActionEventCommand> {

    @Resource
    private FeishuApiService feishuApiService;

    @Resource
    private MeetingEvaluationService meetingEvaluationService;

    @Override
    public void handle(EventCallbackCommand command) {
        try {
            log.info("收到卡片交互事件，原始数据: {}", JsonUtils.toJson(command.getEvent()));

            // 手动解析事件数据
            String eventJson = JsonUtils.toJson(command.getEvent());
            CardActionEventCommand cardEvent = JsonUtils.parse(eventJson, CardActionEventCommand.class);

            log.info("解析后的卡片交互事件，messageId: {}, userId: {}",
                    cardEvent.getContext() != null ? cardEvent.getContext().getOpenMessageId() : "unknown",
                    cardEvent.getOperator() != null ? cardEvent.getOperator().getOpenId() : "unknown");

            // 处理卡片交互逻辑
            handleCardAction(cardEvent);

            // 异步5秒后禁用卡片
            if (cardEvent.getContext() != null) {
                disableCardAfterDelay(cardEvent.getContext().getOpenMessageId());
            }

        } catch (Exception e) {
            log.error("处理卡片交互事件失败", e);
        }
    }

    /**
     * 处理卡片交互逻辑
     */
    private void handleCardAction(CardActionEventCommand cardEvent) {
        if (cardEvent == null || cardEvent.getAction() == null || cardEvent.getOperator() == null) {
            log.warn("卡片事件数据不完整");
            return;
        }

        String userId = cardEvent.getOperator().getOpenId();
        String buttonValue = cardEvent.getAction().getValue();

        log.info("处理卡片交互，用户: {}, 按钮值: {}", userId, buttonValue);

        if (buttonValue == null) {
            log.warn("按钮值为空，无法处理");
            return;
        }

        // 解析按钮值获取操作类型
        String actionValue = null;
        try {
            ActionValueDTO actionValueDTO = JsonUtils.parse(JsonUnescapeUtil.unescape(buttonValue), ActionValueDTO.class);
            actionValue = actionValueDTO.getAction();
        } catch (Exception e) {
            log.error("解析按钮值失败: {}", buttonValue, e);
            return;
        }

        log.info("解析出的操作类型: {}", actionValue);

        // 根据不同的操作值处理不同的业务逻辑
        switch (actionValue) {
            case "view_task_detail":
                handleViewTaskDetail(cardEvent);
                break;
            case "mark_task_complete":
                handleMarkTaskComplete(cardEvent);
                break;
            case "view_meeting_detail":
                handleViewMeetingDetail(cardEvent);
                break;
            case "submit_meeting_evaluation":
                handleSubmitMeetingEvaluation(cardEvent);
                break;
            case "test_action_1":
                handleTestAction1(cardEvent);
                break;
            case "test_action_2":
                handleTestAction2(cardEvent);
                break;
            default:
                log.warn("未知的卡片操作: {}", actionValue);
                break;
        }
    }

    /**
     * 处理查看任务详情
     */
    private void handleViewTaskDetail(CardActionEventCommand cardEvent) {
        log.info("用户 {} 查看任务详情", cardEvent.getOperator().getOpenId());
        // 这里可以记录用户行为、统计点击等
    }

    /**
     * 处理标记任务完成
     */
    private void handleMarkTaskComplete(CardActionEventCommand cardEvent) {
        log.info("用户 {} 标记任务完成", cardEvent.getOperator().getOpenId());
        // 这里可以调用任务完成的业务逻辑
        // 例如：taskActionService.markComplete(taskId, userId);
    }

    /**
     * 处理查看会议详情
     */
    private void handleViewMeetingDetail(CardActionEventCommand cardEvent) {
        log.info("用户 {} 查看会议详情", cardEvent.getOperator().getOpenId());
        // 这里可以记录用户行为、统计点击等
    }

    /**
     * 处理会议评价提交
     */
    private void handleSubmitMeetingEvaluation(CardActionEventCommand cardEvent) {
        try {
            log.info("用户 {} 提交会议评价", cardEvent.getOperator().getOpenId());
            log.info("完整的卡片事件数据: {}", JsonUtils.toJson(cardEvent));

            // 解析按钮值中的会议ID和评价人信息
            String buttonValue = cardEvent.getAction().getValue();
            if (buttonValue == null) {
                log.warn("无法获取按钮值");
                return;
            }
            ActionValueDTO actionValueDTO = JsonUtils.parse(JsonUnescapeUtil.unescape(buttonValue), ActionValueDTO.class);
            Long meetingId = Long.valueOf(actionValueDTO.getMeetingId());
            String evaluatorOpenId = actionValueDTO.getEvaluatorOpenId();

            // 获取表单数据
            Map<String, Object> formData = cardEvent.getAction().getFormValue();
            log.info("表单数据: {}", JsonUtils.toJson(formData));

            if (formData == null || formData.isEmpty()) {
                log.warn("表单数据为空，无法提交评价");
                return;
            }

            // 构建评价数据
            MeetingEvaluationDTO evaluationDTO = MeetingEvaluationDTO.builder()
                .meetingScore(getScoreFromForm(formData, "meeting_score"))
                .contentScore(getScoreFromForm(formData, "content_score"))
                .durationScore(getScoreFromForm(formData, "duration_score"))
                .effectivenessScore(getScoreFromForm(formData, "effectiveness_score"))
                .suggestions(getStringFromForm(formData, "suggestions"))
                .build();

            log.info("构建的评价数据: {}", JsonUtils.toJson(evaluationDTO));

            // 保存评价数据
            meetingEvaluationService.saveMeetingEvaluation(meetingId, evaluatorOpenId, evaluationDTO);

            log.info("会议评价提交成功，会议ID: {}, 评价人: {}", meetingId, evaluatorOpenId);

            // 更新卡片显示提交成功状态
            updateCardAfterSubmission(cardEvent, true, "评价提交成功！感谢您的反馈。");

        } catch (Exception e) {
            log.error("处理会议评价提交失败", e);
            // 更新卡片显示提交失败状态
            updateCardAfterSubmission(cardEvent, false, "评价提交失败，请稍后重试。");
        }
    }

    /**
     * 从表单数据中获取评分
     */
    private Integer getScoreFromForm(Map<String, Object> formData, String key) {
        Object value = formData.get(key);
        if (value != null) {
            try {
                return Integer.valueOf(value.toString());
            } catch (NumberFormatException e) {
                log.warn("无法解析评分值: {}", value);
            }
        }
        return null;
    }

    /**
     * 从表单数据中获取字符串
     */
    private String getStringFromForm(Map<String, Object> formData, String key) {
        Object value = formData.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 处理测试按钮1
     */
    private void handleTestAction1(CardActionEventCommand cardEvent) {
        log.info("用户 {} 点击了测试按钮1", cardEvent.getOperator().getOpenId());
        // 模拟一些业务处理
        log.info("执行测试操作1的业务逻辑...");
    }

    /**
     * 处理测试按钮2
     */
    private void handleTestAction2(CardActionEventCommand cardEvent) {
        log.info("用户 {} 点击了测试按钮2", cardEvent.getOperator().getOpenId());
        // 模拟一些业务处理
        log.info("执行测试操作2的业务逻辑...");
    }

    /**
     * 异步5秒后禁用卡片
     */
    private void disableCardAfterDelay(String messageId) {
        CompletableFuture.runAsync(() -> {
            try {
                // 等待5秒
                TimeUnit.SECONDS.sleep(5);

                // 构建禁用状态的卡片内容
                String disabledCardContent = buildDisabledCardContent();

                // 更新卡片
                feishuApiService.updateMessage(messageId, disabledCardContent);

                log.info("卡片已禁用，messageId: {}", messageId);

            } catch (Exception e) {
                log.error("禁用卡片失败，messageId: {}", messageId, e);
            }
        });
    }

    /**
     * 构建禁用状态的卡片内容
     */
    private String buildDisabledCardContent() {
        return "{\n" +
               "  \"config\": {\n" +
               "    \"wide_screen_mode\": true\n" +
               "  },\n" +
               "  \"header\": {\n" +
               "    \"template\": \"grey\",\n" +
               "    \"title\": {\n" +
               "      \"tag\": \"plain_text\",\n" +
               "      \"content\": \"⚪ 卡片已失效\"\n" +
               "    }\n" +
               "  },\n" +
               "  \"elements\": [\n" +
               "    {\n" +
               "      \"tag\": \"div\",\n" +
               "      \"text\": {\n" +
               "        \"tag\": \"lark_md\",\n" +
               "        \"content\": \"此卡片已失效，请通过其他方式查看详情。\"\n" +
               "      }\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }

    /**
     * 提交后更新卡片状态
     */
    private void updateCardAfterSubmission(CardActionEventCommand cardEvent, boolean success, String message) {
        try {
            String messageId = cardEvent.getContext().getOpenMessageId();
            String updatedCardContent = buildSubmissionResultCard(success, message);

            // 更新卡片内容
            feishuApiService.updateMessage(messageId, updatedCardContent);

            log.info("卡片更新成功，messageId: {}, 状态: {}", messageId, success ? "成功" : "失败");

        } catch (Exception e) {
            log.error("更新卡片失败", e);
        }
    }

    /**
     * 构建提交结果卡片内容
     */
    private String buildSubmissionResultCard(boolean success, String message) {
        String template = success ? "green" : "red";
        String icon = success ? "✅" : "❌";
        String title = success ? "评价提交成功" : "评价提交失败";

        return String.format(
            "{\n" +
            "  \"schema\": \"2.0\",\n" +
            "  \"config\": {\n" +
            "    \"update_multi\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"%s %s\"\n" +
            "    },\n" +
            "    \"template\": \"%s\"\n" +
            "  },\n" +
            "  \"body\": {\n" +
            "    \"direction\": \"vertical\",\n" +
            "    \"elements\": [\n" +
            "      {\n" +
            "        \"tag\": \"markdown\",\n" +
            "        \"content\": \"%s\"\n" +
            "      },\n" +
            "      {\n" +
            "        \"tag\": \"column_set\",\n" +
            "        \"columns\": [\n" +
            "          {\n" +
            "            \"tag\": \"column\",\n" +
            "            \"width\": \"weighted\",\n" +
            "            \"weight\": 1,\n" +
            "            \"elements\": [\n" +
            "              {\n" +
            "                \"tag\": \"button\",\n" +
            "                \"text\": {\n" +
            "                  \"tag\": \"plain_text\",\n" +
            "                  \"content\": \"已提交\"\n" +
            "                },\n" +
            "                \"type\": \"default\",\n" +
            "                \"disabled\": true\n" +
            "              }\n" +
            "            ]\n" +
            "          }\n" +
            "        ]\n" +
            "      }\n" +
            "    ]\n" +
            "  }\n" +
            "}",
            icon, title, template, message
        );
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.CARD_ACTION;
    }
}

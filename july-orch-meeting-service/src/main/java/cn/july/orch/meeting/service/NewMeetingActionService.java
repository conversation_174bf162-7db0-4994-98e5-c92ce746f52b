package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.config.FeishuAppContext;
import cn.july.feishu.model.AttendUserModel;
import cn.july.feishu.model.CreateCalendarEventAttendeesModel;
import cn.july.feishu.model.GetMeetingModel;
import cn.july.orch.meeting.assembler.NewMeetingAssembler;
import cn.july.orch.meeting.common.Constants;
import cn.july.orch.meeting.config.CurrentUserHolder;
import cn.july.orch.meeting.domain.CreateCalendarEventResult;
import cn.july.orch.meeting.domain.command.NewMeetingCreateCommand;
import cn.july.orch.meeting.domain.command.NewMeetingUpdateCommand;
import cn.july.orch.meeting.domain.dto.RecurrenceRule;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import com.lark.oapi.service.vc.v1.model.GetMeetingRespBody;
import com.lark.oapi.service.vc.v1.model.Meeting;
import com.lark.oapi.service.vc.v1.model.MeetingParticipant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 新会议操作服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewMeetingActionService {

    private final NewMeetingDomainService newMeetingDomainService;
    private final NewMeetingAssembler newMeetingAssembler;
    private final FeishuCalendarActionService feishuCalendarActionService;
    private final FeishuAppClient feishuAppClient;
    private final NewMeetingMapper newMeetingMapper;

    /**
     * 创建会议
     */
    @Transactional(rollbackFor = Exception.class)
    public void createMeeting(NewMeetingCreateCommand command) {
        log.info("开始创建新会议，命令：{}", command);

        // 参会人员重复校验
        HashSet<String> userIds = new HashSet<>();
        command.getAttendees().forEach(userId -> {
            if (!userIds.add(userId)) {
                throw new BusinessException(MessageCode.EXIST_PERSONNEL_DUPLICATION, userId);
            }
        });

        // 转换为领域实体
        NewMeeting meeting = newMeetingAssembler.toEntity(command);

        // 处理重复会议相关字段
        if (command.getRecurrenceRule() != null) {
            meeting.setIsRecurring(IsRecurringEnum.YES);
            meeting.setParentMeetingId(null); // 第一个会议没有父会议
            meeting.setInstanceSequence(1);
            
            // 设置重复规则字段
            setRecurrenceFields(meeting, command.getRecurrenceRule());
        } else {
            meeting.setIsRecurring(IsRecurringEnum.NO);
        }

        // 创建飞书日程
        CreateCalendarEventResult calendarEventResult;
        try {
            calendarEventResult = feishuCalendarActionService.createCalendarEvent(meeting);

            // 设置飞书相关信息
            meeting.setFsCalendarEventId(calendarEventResult.getEventId());
            meeting.setMeetingUrl(calendarEventResult.getMeetingUrl());

            log.info("飞书日程创建成功，事件ID：{}，会议链接：{}",
                calendarEventResult.getEventId(), calendarEventResult.getMeetingUrl());
        } catch (Exception e) {
            log.error("创建飞书日程失败，会议名称：{}", meeting.getMeetingName(), e);
            throw new BusinessException("创建飞书日程失败：" + e.getMessage());
        }

        // 创建会议（落库）
        newMeetingDomainService.createMeeting(meeting);
        meeting.setCreateUserId(CurrentUserHolder.getOpenId());

        // 异步添加参会人员到飞书日程
        if (calendarEventResult != null) {
            addFSCalendarEventAttendeesAsync(meeting, calendarEventResult.getEventId(), FeishuAppContext.get());
        }

        log.info("新会议创建成功，ID：{}", meeting.getId());

        // 发送新会议创建事件
        // CreateNewMeetingEvent.CreateNewMeetingData createNewMeetingData = CreateNewMeetingEvent.CreateNewMeetingData.builder()
        //     .newMeeting(meeting)
        //     .build();
        // springEventPublish.publish(new CreateNewMeetingEvent(createNewMeetingData));
    }

    /**
     * 更新会议
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeeting(NewMeetingUpdateCommand command) {
        log.info("开始更新新会议，命令：{}", command);

        // 获取原有会议信息
        NewMeeting existingMeeting = newMeetingDomainService.findById(command.getId());
        if (existingMeeting == null) {
            throw new RuntimeException("会议不存在");
        }

        // 状态校验：只有未开始的会议可以更新
        if (existingMeeting.getStatus().getCode() > NewMeetingStatusEnum.NOT_STARTED.getCode()) {
            throw new BusinessException(MessageCode.NO_ALLOW_TO_UPDATE);
        }

        // 参会人员重复校验
        HashSet<String> userIds = new HashSet<>();
        command.getAttendees().forEach(userId -> {
            if (!userIds.add(userId)) {
                throw new BusinessException(MessageCode.EXIST_PERSONNEL_DUPLICATION, userId);
            }
        });

        // 转换为领域实体
        NewMeeting meeting = newMeetingAssembler.toEntity(command);

        // 处理重复规则更新
        if (command.getRecurrenceRule() != null) {
            if (existingMeeting.getIsRecurring() == null || existingMeeting.getIsRecurring() == IsRecurringEnum.NO) {
                throw new BusinessException("该会议不是重复会议");
            }
            setRecurrenceFields(meeting, command.getRecurrenceRule());
        }

        // 保留原有会议的飞书相关信息
        meeting.setFsCalendarEventId(existingMeeting.getFsCalendarEventId());
        meeting.setFsMeetingId(existingMeeting.getFsMeetingId());
        meeting.setMeetingUrl(existingMeeting.getMeetingUrl());
        meeting.setMinuteUrl(existingMeeting.getMinuteUrl());
        meeting.setCreateUserId(existingMeeting.getCreateUserId());
        meeting.setCreateUserName(existingMeeting.getCreateUserName());
        meeting.setCreateTime(existingMeeting.getCreateTime());

        // 更新会议
        newMeetingDomainService.updateMeeting(meeting);

        // 更新飞书日程
        if (existingMeeting.getFsCalendarEventId() != null) {
            try {
                feishuCalendarActionService.updateCalendarEvent(meeting);

                // 处理参会人员变更
                handleAttendeeChanges(existingMeeting, meeting);

                log.info("飞书日程更新成功，会议ID：{}", meeting.getId());
            } catch (Exception e) {
                log.error("更新飞书日程失败，会议ID：{}", meeting.getId(), e);
                throw new BusinessException("更新飞书日程失败：" + e.getMessage());
            }
        }
    }

    /**
     * 删除会议
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMeeting(Long id) {
        log.info("开始删除新会议，ID：{}", id);

        // 获取会议信息
        NewMeeting meeting = newMeetingDomainService.findById(id);
        if (meeting == null) {
            return;
        }

        // 状态校验：只有未开始的会议可以删除
        if (meeting.getStatus().getCode() > NewMeetingStatusEnum.NOT_STARTED.getCode()) {
            throw new BusinessException(MessageCode.NO_ALLOW_TO_DELETE);
        }

        // 删除飞书日程
        if (meeting.getFsCalendarEventId() != null) {
            try {
                feishuCalendarActionService.deleteCalendarEvent(meeting.getFsCalendarEventId());
                log.info("飞书日程删除成功，会议ID：{}", id);
            } catch (Exception e) {
                log.error("删除飞书日程失败，会议ID：{}", id, e);
                throw new BusinessException("删除飞书日程失败：" + e.getMessage());
            }
        }

        // 删除会议
        newMeetingDomainService.deleteMeeting(id);
        log.info("新会议删除成功，ID：{}", id);
    }

    /**
     * 设置重复规则字段
     */
    private void setRecurrenceFields(NewMeeting meeting, RecurrenceRule rule) {
        meeting.setRecurrenceType(rule.getRecurrenceType());
        meeting.setRecurrenceInterval(rule.getRecurrenceInterval());
        meeting.setRecurrenceWeekdays(rule.getRecurrenceWeekdays() != null ? 
            rule.getRecurrenceWeekdays().stream().map(String::valueOf).collect(Collectors.joining(",")) : null);
        meeting.setRecurrenceMonthDay(rule.getRecurrenceMonthDay());
        meeting.setRecurrenceEndDate(rule.getRecurrenceEndDate());
    }

    /**
     * 更新会议状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeetingStatus(Long id, NewMeetingStatusEnum status) {
        log.info("开始更新会议状态，ID：{}，状态：{}", id, status);
        newMeetingDomainService.updateMeetingStatus(id, status);
        log.info("会议状态更新成功，ID：{}，状态：{}", id, status);
    }

    /**
     * 处理飞书回调 - 会议状态变更
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuStatusCallback(String fsCalendarEventId, NewMeetingStatusEnum status) {
        log.info("处理飞书状态回调，日程事件ID：{}，状态：{}", fsCalendarEventId, status);

        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting != null) {
            newMeetingDomainService.updateMeetingStatus(meeting.getId(), status);
            log.info("飞书状态回调处理成功，会议ID：{}，状态：{}", meeting.getId(), status);
        } else {
            log.warn("未找到对应的会议，飞书日程事件ID：{}", fsCalendarEventId);
        }
    }

    /**
     * 处理飞书回调 - 会议信息变更
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuInfoCallback(String fsCalendarEventId, String meetingName, String meetingLocation,
                                       String startTime, String endTime) {
        log.info("处理飞书信息回调，日程事件ID：{}", fsCalendarEventId);

        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting != null) {
            // 更新会议信息
            meeting.setMeetingName(meetingName);
            meeting.setMeetingLocation(meetingLocation);
            // 这里可以添加时间转换逻辑

            newMeetingDomainService.updateMeeting(meeting);
            log.info("飞书信息回调处理成功，会议ID：{}", meeting.getId());
        } else {
            log.warn("未找到对应的会议，飞书日程事件ID：{}", fsCalendarEventId);
        }
    }

    /**
     * 处理飞书回调 - 会议开始事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuMeetingStartCallback(String fsCalendarEventId, String meetingNo, String fsMeetingId, String startTime) {
        log.info("处理飞书会议开始回调，日程事件ID：{}，会议编号：{}", fsCalendarEventId, meetingNo);

        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting != null) {
            // 生成会议链接
//            String meetingUrl = feishuCalendarActionService.getMeetingUrl(fsMeetingId);
            String meetingUrl = null;

            // 更新会议编号、飞书会议ID和会议链接
            newMeetingDomainService.updateMeetingInfo(meeting.getId(), meetingNo, fsMeetingId, meetingUrl);

            // 更新会议状态为进行中
            newMeetingDomainService.updateMeetingStatus(meeting.getId(), NewMeetingStatusEnum.IN_PROCESS);

            log.info("飞书会议开始回调处理成功，会议ID：{}，会议编号：{}", meeting.getId(), meetingNo);
        } else {
            log.warn("未找到对应的会议，飞书日程事件ID：{}", fsCalendarEventId);
        }
    }

    /**
     * 处理飞书回调 - 会议结束事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuMeetingEndCallback(String fsCalendarEventId, String endTime) {
        log.info("处理飞书会议结束回调，日程事件ID：{}", fsCalendarEventId);

        NewMeeting meeting = newMeetingDomainService.findByFsCalendarEventId(fsCalendarEventId);
        if (meeting != null) {
            // 更新会议状态为已结束
            newMeetingDomainService.updateMeetingStatus(meeting.getId(), NewMeetingStatusEnum.ENDED);

            // 获取飞书会议ID，如果存在则获取实际会议信息
            if (meeting.getFsMeetingId() != null && !meeting.getFsMeetingId().isEmpty()) {
                try {
                    updateMeetingActualInfoFromFeishu(meeting.getId(), meeting.getFsMeetingId());
                } catch (Exception e) {
                    log.error("获取飞书会议实际信息失败，会议ID：{}", meeting.getId(), e);
                }
            }

            // 如果是重复会议，生成下次会议
            if (meeting.getIsRecurring() != null && meeting.getIsRecurring() == IsRecurringEnum.YES) {
                try {
                    generateNextMeetingAfterEnd(meeting.getId());
                } catch (Exception e) {
                    log.error("生成下次会议失败，会议ID：{}", meeting.getId(), e);
                }
            }

            log.info("飞书会议结束回调处理成功，会议ID：{}", meeting.getId());
        } else {
            log.warn("未找到对应的会议，飞书日程事件ID：{}", fsCalendarEventId);
        }
    }

    /**
     * 会议结束后自动生成下次会议
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateNextMeetingAfterEnd(Long meetingId) {
        NewMeeting currentMeeting = newMeetingDomainService.findById(meetingId);
        if (currentMeeting == null || currentMeeting.getIsRecurring() == null || currentMeeting.getIsRecurring() == IsRecurringEnum.NO) {
            return;
        }

        // 检查是否可以生成下次会议
        if (!currentMeeting.canGenerateNextMeeting()) {
            log.info("会议{}已达到重复结束条件，不再生成下次会议", meetingId);
            return;
        }

        // 检查重复系列是否已取消
        if (currentMeeting.getRecurrenceCancelled() != null && currentMeeting.getRecurrenceCancelled() == 1) {
            log.info("会议{}的重复系列已取消，不再生成下次会议", meetingId);
            return;
        }

        // 计算下次会议时间
        LocalDateTime nextStartTime = currentMeeting.calculateNextMeetingTime();
        if (nextStartTime == null) {
            return;
        }

        // 创建下次会议
        NewMeeting nextMeeting = createNextMeeting(currentMeeting, nextStartTime);

        // 创建飞书日程
        try {
            CreateCalendarEventResult calendarEventResult = feishuCalendarActionService.createCalendarEvent(nextMeeting);
            nextMeeting.setFsCalendarEventId(calendarEventResult.getEventId());
            nextMeeting.setMeetingUrl(calendarEventResult.getMeetingUrl());
        } catch (Exception e) {
            log.error("创建飞书日程失败，会议名称：{}", nextMeeting.getMeetingName(), e);
            // 继续创建会议，但不创建飞书日程
        }

        // 保存下次会议
        newMeetingDomainService.createMeeting(nextMeeting);

        log.info("自动生成下次会议成功，原会议ID：{}，新会议ID：{}", meetingId, nextMeeting.getId());
    }

    /**
     * 创建下次会议
     */
    private NewMeeting createNextMeeting(NewMeeting currentMeeting, LocalDateTime nextStartTime) {
        int duration = currentMeeting.calculateDuration();
        LocalDateTime nextEndTime = nextStartTime.plusMinutes(duration);

        return NewMeeting.builder()
            .meetingName(currentMeeting.getMeetingName())
            .meetingDescription(currentMeeting.getMeetingDescription())
            .meetingPlanId(currentMeeting.getMeetingPlanId())
            .meetingStandardId(currentMeeting.getMeetingStandardId())
            .meetingNo(generateNextMeetingNo(currentMeeting.getMeetingNo()))
            .startTime(nextStartTime)
            .endTime(nextEndTime)
            .status(NewMeetingStatusEnum.NOT_STARTED)
            .priorityLevel(currentMeeting.getPriorityLevel())
            .meetingLocation(currentMeeting.getMeetingLocation())
            .attendees(currentMeeting.getAttendees())
            .hostUserId(currentMeeting.getHostUserId())
            .recorderUserId(currentMeeting.getRecorderUserId())
            .isRecurring(IsRecurringEnum.YES)
            .recurrenceType(currentMeeting.getRecurrenceType())
            .recurrenceInterval(currentMeeting.getRecurrenceInterval())
            .recurrenceWeekdays(currentMeeting.getRecurrenceWeekdays())
            .recurrenceMonthDay(currentMeeting.getRecurrenceMonthDay())
            .recurrenceEndDate(currentMeeting.getRecurrenceEndDate())
            .parentMeetingId(currentMeeting.getParentMeetingId() != null ? 
                currentMeeting.getParentMeetingId() : currentMeeting.getId())
            .instanceSequence(currentMeeting.getInstanceSequence() + 1)
            .recurrenceCancelled(0) // 新生成的会议默认未取消
            .createUserId(currentMeeting.getCreateUserId())
            .createUserName(currentMeeting.getCreateUserName())
            .createTime(LocalDateTime.now())
            .build();
    }

    /**
     * 生成下次会议编号
     */
    private String generateNextMeetingNo(String currentMeetingNo) {
        if (currentMeetingNo == null || currentMeetingNo.isEmpty()) {
            return "MEETING_" + System.currentTimeMillis();
        }

        // 如果会议编号包含序号，则递增序号
        if (currentMeetingNo.contains("_")) {
            String[] parts = currentMeetingNo.split("_");
            if (parts.length > 1) {
                try {
                    int sequence = Integer.parseInt(parts[parts.length - 1]);
                    return currentMeetingNo.substring(0, currentMeetingNo.lastIndexOf("_")) + "_" + (sequence + 1);
                } catch (NumberFormatException e) {
                    // 如果最后一部分不是数字，直接添加序号
                }
            }
        }

        return currentMeetingNo + "_" + System.currentTimeMillis();
    }

    /**
     * 处理飞书回调 - 会议录制完成事件
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleFeishuRecordReadyCallback(String fsMeetingId, String minuteUrl) {
        log.info("处理飞书会议录制完成回调，飞书会议ID：{}", fsMeetingId);

        NewMeeting meeting = newMeetingDomainService.findByFsMeetingId(fsMeetingId);
        if (meeting != null) {
            // 更新妙计链接
            newMeetingDomainService.updateMinuteUrl(meeting.getId(), minuteUrl);
            //妙计视频转文字
            newMeetingDomainService.getMinuteText(meeting.getId(),meeting.getHostUserId(), minuteUrl);

            log.info("飞书会议录制完成回调处理成功，会议ID：{}，妙计链接：{}", meeting.getId(), minuteUrl);
        } else {
            log.warn("未找到对应的会议，飞书会议ID：{}", fsMeetingId);
        }
    }

    /**
     * 异步添加飞书日程参会人员
     */
    @Async
    public void addFSCalendarEventAttendeesAsync(NewMeeting meeting, String fsCalendarEventId, FeishuAppContext feishuAppContext) {
        try {
            // 异步上下文传递
            FeishuAppContext.set(feishuAppContext);

            if (meeting.getAttendees() != null && !meeting.getAttendees().isEmpty()) {
                // 构建参会人员模型
                List<AttendUserModel> attendUsers = meeting.getAttendees().stream()
                    .map(userId -> AttendUserModel.builder()
                        .userId(userId)
                        .isOrganizer(meeting.getCreateUserId().equals(userId))
                        .build())
                    .collect(Collectors.toList());

                // 调用飞书API添加参会人员
                feishuAppClient.getCalendarEventService().createAttendees(CreateCalendarEventAttendeesModel.builder()
                    .calendarId(feishuCalendarActionService.getPrimaryCalendarId())
                    .eventId(fsCalendarEventId)
                    .attendUsers(attendUsers)
                    .build());

                log.info("异步添加飞书日程参会人员成功，会议ID：{}", meeting.getId());
            }
        } catch (Exception e) {
            log.error("异步添加飞书日程参会人员失败，会议ID：{}", meeting.getId(), e);
        } finally {
            // 异步上下文清除
            FeishuAppContext.remove();
        }
    }

    /**
     * 处理参会人员变更
     */
    private void handleAttendeeChanges(NewMeeting oldMeeting, NewMeeting newMeeting) {
        // 这里可以实现参会人员的增删改逻辑
        // 由于简化设计，这里只做基本处理
        // 实际项目中可能需要更复杂的逻辑来处理参会人员的变更

        if (oldMeeting.getAttendees() != null && newMeeting.getAttendees() != null) {
            // 找出新增的参会人员
            List<String> addedAttendees = newMeeting.getAttendees().stream()
                .filter(attendee -> !oldMeeting.getAttendees().contains(attendee))
                .collect(Collectors.toList());

            // 找出删除的参会人员
            List<String> removedAttendees = oldMeeting.getAttendees().stream()
                .filter(attendee -> !newMeeting.getAttendees().contains(attendee))
                .collect(Collectors.toList());

            // 添加新参会人员
            if (!addedAttendees.isEmpty()) {
                feishuCalendarActionService.addCalendarEventAttendees(
                    newMeeting.getFsCalendarEventId(),
                    addedAttendees,
                    newMeeting.getCreateUserId()
                );
            }

            // 删除参会人员
            if (!removedAttendees.isEmpty()) {
                feishuCalendarActionService.deleteCalendarEventAttendees(
                    newMeeting.getFsCalendarEventId(),
                    removedAttendees
                );
            }
        }
    }

    /**
     * 取消重复系列
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelRecurrenceSeries(Long meetingId, String reason) {
        NewMeeting meeting = newMeetingDomainService.findById(meetingId);
        if (meeting == null) {
            throw new RuntimeException("会议不存在");
        }

        if (meeting.getIsRecurring() == null || meeting.getIsRecurring() == IsRecurringEnum.NO) {
            throw new RuntimeException("该会议不是重复会议");
        }

        // 设置重复系列为已取消
        meeting.setRecurrenceCancelled(1);
        
        // 更新会议
        newMeetingDomainService.updateMeeting(meeting);
        
        log.info("取消重复系列成功，会议ID：{}，原因：{}", meetingId, reason);
    }

    /**
     * 从飞书获取会议实际执行信息并更新
     */
    private void updateMeetingActualInfoFromFeishu(Long meetingId, String fsMeetingId) {
        try {
            // 调用飞书API获取会议详情
            GetMeetingModel fsMeetingReq = GetMeetingModel.builder()
                    .meetingId(fsMeetingId)
                    .withParticipants(true)
                    .userIdType(Constants.OPEN_ID)
                    .build();
            GetMeetingRespBody response = feishuAppClient.getMeetService().getMeeting(fsMeetingReq);

            if (response != null && response.getMeeting() != null) {
                Meeting fsMeeting = response.getMeeting();
                
                // 获取实际开始时间和结束时间
                LocalDateTime actualStartTime = null;
                LocalDateTime actualEndTime = null;
                
                if (fsMeeting.getStartTime() != null) {
                    actualStartTime = LocalDateTime.parse(fsMeeting.getStartTime().replace("Z", ""));
                }
                if (fsMeeting.getEndTime() != null) {
                    actualEndTime = LocalDateTime.parse(fsMeeting.getEndTime().replace("Z", ""));
                }

                // 获取实际参会人员open_id列表
                List<String> actualAttendees = null;
                if (fsMeeting.getParticipants() != null && fsMeeting.getParticipants().length>0) {
                    actualAttendees = Arrays.stream(fsMeeting.getParticipants())
                        .map(MeetingParticipant::getId)
                        .collect(Collectors.toList());
                }

                // 更新会议实际执行信息
                NewMeetingPO meetingPO = newMeetingMapper.selectById(meetingId);
                if (meetingPO != null) {
                    meetingPO.setActualStartTime(actualStartTime);
                    meetingPO.setActualEndTime(actualEndTime);
                    meetingPO.setActualAttendees(actualAttendees);

                    newMeetingMapper.updateById(meetingPO);
                    
                    log.info("会议实际执行信息更新成功，会议ID：{}，实际开始时间：{}，实际结束时间：{}，实际参会人数：{}", 
                            meetingId, actualStartTime, actualEndTime, 
                            actualAttendees != null ? actualAttendees.size() : 0);
                }
            }
        } catch (Exception e) {
            log.error("从飞书获取会议实际信息失败，会议ID：{}，飞书会议ID：{}，错误：{}", 
                    meetingId, fsMeetingId, e.getMessage(), e);
            throw new RuntimeException("获取飞书会议实际信息失败", e);
        }
    }
}

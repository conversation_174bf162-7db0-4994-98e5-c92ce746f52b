package cn.july.orch.meeting.service;

import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.domain.dto.card.SendMeetingNotificationDTO;
import cn.july.orch.meeting.domain.entity.MeetingPlan;
import cn.july.orch.meeting.domain.entity.MeetingStandard;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import cn.july.orch.meeting.properties.MeetingSeverProperties;
import cn.july.orch.meeting.repository.IMeetingPlanRepository;
import cn.july.orch.meeting.repository.IMeetingStandardRepository;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议通知服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingNotificationActionService {

    private final IMeetingPlanRepository meetingPlanRepository;
    private final IMeetingStandardRepository meetingStandardRepository;

    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private MeetingSeverProperties meetingSeverProperties;
    @Resource
    private FeishuAppClient feishuAppClient;

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 发送会议提前通知
     */
    public void sendAdvanceNotifications() {
        log.info("开始执行会议提前通知任务");

        try {
            // 1. 获取所有启用的会议标准
            List<MeetingStandard> standards = meetingStandardRepository.findAllEnabled();

            // 2. 对每个标准处理通知
            for (MeetingStandard standard : standards) {
                processStandardNotifications(standard);
            }

            log.info("会议提前通知任务执行完成");
        } catch (Exception e) {
            log.error("会议提前通知任务执行失败", e);
        }
    }

    /**
     * 处理单个会议标准的通知
     */
    private void processStandardNotifications(MeetingStandard standard) {
        // 1. 计算提前通知时间（转换为分钟）
        int noticeMinutes = calculateNoticeMinutes(standard.getAdvanceNoticeValue(), standard.getAdvanceNoticeUnit());

        if (noticeMinutes <= 0) {
            return; // 没有设置提前通知时间，跳过
        }

        // 2. 计算通知时间窗口
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime noticeStartTime = now.minusMinutes(1); // 避免重复发送
        LocalDateTime noticeEndTime = now.plusMinutes(1);

        // 3. 查询符合条件的会议规划
        List<MeetingPlan> plans = findPlansNeedNotification(standard.getId(), noticeStartTime, noticeEndTime, noticeMinutes);

        // 4. 发送通知
        for (MeetingPlan plan : plans) {
            sendMeetingNotification(plan, standard, noticeMinutes);
        }
    }

    /**
     * 查询需要发送通知的会议规划
     */
    private List<MeetingPlan> findPlansNeedNotification(Long standardId, LocalDateTime startTime, LocalDateTime endTime, int noticeMinutes) {
        return meetingPlanRepository.findPlansForNotification(standardId, startTime, endTime, noticeMinutes);
    }

    /**
     * 计算提前通知时间（分钟）
     */
    private int calculateNoticeMinutes(Integer value, TimeUnitEnum unit) {
        if (value == null || unit == null) {
            return 0;
        }

        switch (unit) {
            case MINUTE:
                return value;
            case HOUR:
                return value * 60;
            case DAY:
                return value * 24 * 60;
            case WEEK:
                return value * 7 * 24 * 60;
            default:
                return 0;
        }
    }

        /**
     * 发送会议通知
     */
    private void sendMeetingNotification(MeetingPlan plan, MeetingStandard standard, int noticeMinutes) {
        try {
            // 1. 构建通知卡片数据
            List<SendMeetingNotificationDTO> list = buildNotificationDTO(plan, standard, noticeMinutes);
            
            // 2. 发送飞书卡片
            list.forEach(cardSendActionService::sendMeetingNotification);

            // 3. 更新发送标记
            meetingPlanRepository.updateAdvanceNoticeSent(plan.getId(), 1);
            
            log.info("会议通知发送成功，会议规划ID: {}, 会议名称: {}", plan.getId(), plan.getPlanName());
        } catch (Exception e) {
            log.error("会议通知发送失败，会议规划ID: {}, 会议名称: {}", plan.getId(), plan.getPlanName(), e);
        }
    }

    /**
     * 构建通知卡片数据
     */
    private List<SendMeetingNotificationDTO> buildNotificationDTO(MeetingPlan plan, MeetingStandard standard, int noticeMinutes) {
        // 获取参会人员名称列表
        List<SendMeetingNotificationDTO> resultList = new ArrayList<>();
        List<String> attendeeNames = getAttendeeNames(plan.getAttendees());
        for (String attendee : plan.getAttendees()) {
            SendMeetingNotificationDTO dto = SendMeetingNotificationDTO.builder()
                    .openId(attendee) // 这里需要根据实际需求获取openId
                    .meetingType(standard.getStandardName())
                    .meetingName(plan.getPlanName())
                    .meetingTime(formatMeetingTime(plan.getPlannedStartTime(), plan.getPlannedEndTime()))
                    .meetingLocation(plan.getMeetingLocation())
                    .meetingDuration(formatDuration(plan.getPlannedDuration()))
                    .attendees(attendeeNames) // 使用用户名称而不是openId
                    .meetingDescription(plan.getPlanDescription())
                    .priorityLevel(formatPriority(plan.getPriorityLevel()))
                    .noticeType("提前通知")
                    .noticeTime(formatNoticeTime(noticeMinutes))
                    .meetingUrl(buildMeetingUrl(plan.getId()))
                    .build();
            resultList.add(dto);
        }
        return resultList;
    }

    /**
     * 获取参会人员名称列表
     * 根据openId查询用户信息，如果查询不到则使用openId显示
     */
    private List<String> getAttendeeNames(List<String> attendeeOpenIds) {
        if (attendeeOpenIds == null || attendeeOpenIds.isEmpty()) {
            return attendeeOpenIds;
        }

        try {
            // 批量查询用户信息
            List<User> users = feishuAppClient.getContactService().userBatch(attendeeOpenIds);
            Map<String, User> userMap = users.stream()
                .collect(Collectors.toMap(User::getOpenId, Function.identity()));

            // 构建参会人员名称列表
            return attendeeOpenIds.stream()
                .map(openId -> {
                    User user = userMap.get(openId);
                    // 如果查询到用户信息，使用用户名称；否则使用openId
                    return user != null ? user.getName() : openId;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询参会人员信息失败，使用openId显示", e);
            // 查询失败时，直接返回openId列表
            return attendeeOpenIds;
        }
    }

    /**
     * 获取参会人员的openId
     * 使用会议规划的创建者ID作为openId
     */
    private String getOpenIdForAttendees(MeetingPlan plan) {
        // createUserId实际上就是openId，直接返回
        return plan.getCreateUserId();
    }

    /**
     * 构建会议详情链接
     */
    private String buildMeetingUrl(Long planId) {
        // 参照其他卡片的链接配置方式
        return String.format("%s/meeting-plan/detail?id=%d",
            meetingSeverProperties.getCardSend().getJumpUrl().getMeetingDetailUrl(), planId);
    }

    /**
     * 格式化会议时间
     */
    private String formatMeetingTime(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null) {
            return "时间待定";
        }

        String startTimeStr = startTime.format(TIME_FORMATTER);
        if (endTime == null) {
            return startTimeStr;
        }

        String endTimeStr = endTime.format(TIME_FORMATTER);
        return startTimeStr + " - " + endTimeStr;
    }

    /**
     * 格式化会议时长
     */
    private String formatDuration(Integer duration) {
        if (duration == null) {
            return "时长待定";
        }

        if (duration < 60) {
            return duration + "分钟";
        } else {
            int hours = duration / 60;
            int minutes = duration % 60;
            if (minutes == 0) {
                return hours + "小时";
            } else {
                return hours + "小时" + minutes + "分钟";
            }
        }
    }

    /**
     * 格式化优先级
     */
    private String formatPriority(PriorityLevelEnum priority) {
        if (priority == null) {
            return "普通";
        }

        switch (priority) {
            case LOW:
                return "低";
            case MEDIUM:
                return "中";
            case HIGH:
                return "高";
            case URGENT:
                return "紧急";
            default:
                return "普通";
        }
    }

    /**
     * 格式化提前通知时间
     */
    private String formatNoticeTime(int noticeMinutes) {
        if (noticeMinutes < 60) {
            return noticeMinutes + "分钟";
        } else if (noticeMinutes < 24 * 60) {
            int hours = noticeMinutes / 60;
            return hours + "小时";
        } else {
            int days = noticeMinutes / (24 * 60);
            return days + "天";
        }
    }
}

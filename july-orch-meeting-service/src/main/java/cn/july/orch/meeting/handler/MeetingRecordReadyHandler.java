package cn.july.orch.meeting.handler;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.model.callback.EventCallbackCommand;
import cn.july.orch.meeting.domain.command.MeetingRecordReadyEventCommand;
import cn.july.orch.meeting.enums.EventCallbackEnum;
import cn.july.orch.meeting.service.NewMeetingActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议录制完成事件处理器
 * @date 2025-01-07
 */
@Slf4j
@Component
public class MeetingRecordReadyHandler implements CallbackHandler<MeetingRecordReadyEventCommand> {

    @Resource
    private NewMeetingActionService newMeetingActionService;

    @Override
    public void handle(EventCallbackCommand command) {
        String meetingJson = JsonUtils.toJson(command.getEvent());
        MeetingRecordReadyEventCommand fsCommand = JsonUtils.parse(meetingJson, MeetingRecordReadyEventCommand.class);

        // 新会议妙计链接同步
        newMeetingActionService.handleFeishuRecordReadyCallback(
            fsCommand.getMeeting().getId(),
            fsCommand.getUrl()
        );
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_RECORD_READY;
    }
}

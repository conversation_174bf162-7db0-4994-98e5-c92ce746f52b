package cn.july.orch.meeting.domain.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 卡片交互事件命令
 */
@Data
public class CardActionEventCommand {

    /**
     * 操作者信息
     */
    private Operator operator;

    /**
     * 令牌
     */
    private String token;

    /**
     * 操作信息
     */
    private Action action;

    /**
     * 主机类型
     */
    private String host;

    /**
     * 上下文信息
     */
    private Context context;

    @Data
    public static class Operator {
        /**
         * 租户Key
         */
        @JsonProperty("tenant_key")
        private String tenantKey;

        /**
         * 用户OpenID
         */
        @JsonProperty("open_id")
        private String openId;

        /**
         * 用户UnionID
         */
        @JsonProperty("union_id")
        private String unionId;
    }

    @Data
    public static class Action {
        /**
         * 操作值（JSON对象）
         */
        private ActionValue value;

        /**
         * 操作标签
         */
        private String tag;

        /**
         * 表单数据
         */
        private Map<String, Object> form;
    }

    @Data
    public static class ActionValue {
        /**
         * 操作类型
         */
        private String action;

        /**
         * 操作数据
         */
        private String data;
    }

    @Data
    public static class Context {
        /**
         * 消息ID
         */
        @JsonProperty("open_message_id")
        private String openMessageId;

        /**
         * 群聊ID
         */
        @JsonProperty("open_chat_id")
        private String openChatId;
    }
}

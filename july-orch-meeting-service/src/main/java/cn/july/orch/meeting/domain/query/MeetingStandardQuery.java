package cn.july.orch.meeting.domain.query;

import cn.july.orch.meeting.enums.PriorityLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 会议标准查询
 * @date 2025-01-24
 */
@Data
public class MeetingStandardQuery {

    @ApiModelProperty(value = "标准名称(模糊查询)")
    private String standardName;

    @ApiModelProperty(value = "默认优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "是否启用")
    private Integer isEnabled;

    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize = 10;
}

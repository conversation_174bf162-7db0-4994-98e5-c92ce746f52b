package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.TaskPO;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务Mapper
 */
public interface TaskMapper extends BaseMapper<TaskPO> {

    /**
     * 查询超期任务
     *
     * @param currentTime 当前时间
     * @return 超期任务列表
     */
    List<TaskPO> findOverdueTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据会议ID查询任务列表
     *
     * @param meetingId 会议ID
     * @return 任务列表
     */
    List<TaskPO> findByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据状态查询任务列表
     *
     * @param status 任务状态
     * @return 任务列表
     */
    List<TaskPO> findByStatus(@Param("status") TaskStatusEnum status);

    /**
     * 根据负责人查询任务列表
     *
     * @param ownerOpenId 负责人OpenID
     * @return 任务列表
     */
    List<TaskPO> findByOwner(@Param("ownerOpenId") String ownerOpenId);

    /**
     * 根据飞书任务ID查询任务
     *
     * @param feishuTaskId 飞书任务ID
     * @return 任务
     */
    TaskPO findByFeishuTaskId(@Param("feishuTaskId") String feishuTaskId);
}

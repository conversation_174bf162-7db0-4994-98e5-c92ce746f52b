package cn.july.orch.meeting.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 会议评价数据DTO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEvaluationDTO {
    
    private Integer meetingScore; // 1-5分
    private Integer contentScore; // 1-5分
    private Integer durationScore; // 1-5分
    private Integer effectivenessScore; // 1-5分
    private String suggestions;
} 
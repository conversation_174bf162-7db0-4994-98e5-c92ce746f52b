package cn.july.orch.meeting.service;


import cn.july.orch.meeting.domain.agg.MeetingEvaluationAgg;

/**
 * <AUTHOR>
 * @description 会议评价领域服务
 * @date 2025-01-24
 */
public interface MeetingEvaluationDomainService {
    
    /**
     * 保存评价
     */
    void saveEvaluation(MeetingEvaluationAgg evaluationAgg);
    
    /**
     * 根据会议ID和评价人查询评价
     */
    MeetingEvaluationAgg findByMeetingIdAndEvaluator(Long meetingId, String evaluatorOpenId);
} 
package cn.july.orch.meeting.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 创建日历事件结果
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateCalendarEventResult {

    /**
     * 飞书日历事件ID
     */
    private String eventId;

    /**
     * 飞书会议链接
     */
    private String meetingUrl;
} 
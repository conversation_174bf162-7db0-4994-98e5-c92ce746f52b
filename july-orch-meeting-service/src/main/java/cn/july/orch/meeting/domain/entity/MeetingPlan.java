package cn.july.orch.meeting.domain.entity;

import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划实体
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingPlan {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议规划名称
     */
    private String planName;

    /**
     * 会议规划描述/备注
     */
    private String planDescription;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 计划持续时长(分钟)
     */
    private Integer plannedDuration;

    /**
     * 会议规划状态
     */
    private MeetingPlanStatusEnum status;

    /**
     * 会议标准ID
     */
    private Long meetingStandardId;

    /**
     * 优先级
     */
    private PriorityLevelEnum priorityLevel;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 业务会议ID
     */
    private Long businessMeetingId;

    /**
     * 参会人员列表
     */
    private List<String> attendees;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 提前通知发送标记(0-未发送,1-已发送)
     */
    private Integer advanceNoticeSent;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否重复会议
     */
    private IsRecurringEnum isRecurring;

    /**
     * 重复类型
     */
    private RecurrenceTypeEnum recurrenceType;

    /**
     * 重复间隔
     */
    private Integer recurrenceInterval;

    /**
     * 每周重复的星期几
     */
    private String recurrenceWeekdays;

    /**
     * 每月重复的日期
     */
    private Integer recurrenceMonthDay;

    /**
     * 重复结束日期
     */
    private LocalDate recurrenceEndDate;

    /**
     * 会前文档文件ID列表
     */
    private List<String> preMeetingDocuments;

    /**
     * 计算持续时长
     */
    public void calculateDuration() {
        if (plannedStartTime != null && plannedEndTime != null) {
            this.plannedDuration = (int) java.time.Duration.between(plannedStartTime, plannedEndTime).toMinutes();
        }
    }

    /**
     * 检查是否已逾期
     */
    public boolean isOverdue() {
        return status == MeetingPlanStatusEnum.NOT_STARTED && 
               plannedStartTime != null && 
               plannedStartTime.isBefore(LocalDateTime.now());
    }
}

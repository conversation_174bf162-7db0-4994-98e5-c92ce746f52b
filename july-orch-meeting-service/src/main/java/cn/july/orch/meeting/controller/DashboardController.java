package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.dto.DashboardKpiDTO;
import cn.july.orch.meeting.domain.dto.DashboardTrendDTO;
import cn.july.orch.meeting.domain.dto.RecentMeetingDTO;
import cn.july.orch.meeting.domain.query.DashboardKpiQuery;
import cn.july.orch.meeting.domain.query.DashboardTrendQuery;
import cn.july.orch.meeting.domain.query.RecentMeetingQuery;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import cn.july.orch.meeting.service.DashboardQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @description 仪表板控制器
 * @date 2025-01-24
 */
@Api(tags = "仪表板")
@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    @Resource
    private DashboardQueryService dashboardQueryService;

    @PostMapping("/kpi")
    @ApiOperation(value = "获取KPI数据")
    public DashboardKpiDTO getKpi(@Validated @RequestBody DashboardKpiQuery query) {
        // 如果只传了时间单位，自动计算开始和结束日期
        if (query.getTimeUnit() != null && query.getStartDate() == null && query.getEndDate() == null) {
            LocalDate[] dateRange = calculateDateRangeByUnit(query.getTimeUnit());
            query.setStartDate(dateRange[0]);
            query.setEndDate(dateRange[1]);
        }
        
        return dashboardQueryService.getKpi(query);
    }

    /**
     * 根据时间单位计算日期范围
     * @param timeUnit 时间单位枚举
     * @return [开始日期, 结束日期]
     */
    private LocalDate[] calculateDateRangeByUnit(TimeUnitEnum timeUnit) {
        LocalDate now = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;
        
        switch (timeUnit) {
            case DAY:
                // 今天
                startDate = now;
                endDate = now;
                break;
                
            case WEEK:
                // 本周（周一到周日）
                startDate = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                endDate = now.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
                break;
                
            case MONTH:
                // 本月（1号到月末）
                startDate = now.withDayOfMonth(1);
                endDate = now.with(TemporalAdjusters.lastDayOfMonth());
                break;
                
            case YEAR:
                // 本年（1月1日到12月31日）
                startDate = now.withDayOfYear(1);
                endDate = now.withDayOfYear(now.lengthOfYear());
                break;
                
            default:
                throw new IllegalArgumentException("不支持的时间单位: " + timeUnit.getCode());
        }
        
        return new LocalDate[]{startDate, endDate};
    }

    @PostMapping("/trend")
    @ApiOperation(value = "获取趋势分析数据")
    public DashboardTrendDTO getTrend(@Validated @RequestBody DashboardTrendQuery query) {
        // 如果只传了时间单位，自动计算开始和结束日期
        if (query.getTimeUnit() != null && query.getStartDate() == null && query.getEndDate() == null) {
            LocalDate[] dateRange = calculateDateRangeByUnit(query.getTimeUnit());
            query.setStartDate(dateRange[0]);
            query.setEndDate(dateRange[1]);
        }
        
        return dashboardQueryService.getTrend(query);
    }

    @PostMapping("/recent-meetings")
    @ApiOperation(value = "获取近期会议列表")
    public PageResultDTO<RecentMeetingDTO> getRecentMeetings(@Validated @RequestBody RecentMeetingQuery query) {
        return dashboardQueryService.getRecentMeetings(query);
    }
} 
package cn.july.orch.meeting.domain.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 智能体回答响应对象
 * @date 2025-05-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentCompleteRespDTO {

    /**
     * 回答内容
     */
    private String answer;

    /**
     * 交互式内容
     */
    private InteractiveContent interactive;

    @Data
    public static class InteractiveContent {
        private String type; // "userInput" or "userSelect"
        private Params params;
    }

    @Data
    public static class Params {
        private String description;
        // For userInput
        private List<InputFormItem> inputForm;
        // For userSelect
        private List<UserSelectOption> userSelectOptions;
    }

    @Data
    public static class UserSelectOption {

        private String key;

        private String value;
    }

    @Data
    public static class InputFormItem {

        private String type;
        private String key;
        private String label;
        private String description;
        private String value;
        private String defaultValue;
        private String valueType;
        private boolean required;
        private Integer maxLength;
        private List<InputSelectOption> list;
    }

    @Data
    public static class InputSelectOption {

        private String label;

        private String value;
    }
}

package cn.july.orch.meeting.domain.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 日程变更事件命令
 */
@Data
public class ScheduleChangeEventCommand {

    @JsonProperty("calendar_id")
    private String calendarId;

    @JsonProperty("calendar_event_id")
    private String calendarEventId;

    /**
     * create：日程在日历上被创建。新建日程或者作为参与人被邀请进日程，都属于 create 类型。
     * update：日程发生了变更。
     * delete：日程从日历上消失。删除日程或者作为参与人被移出了日程，都属于 delete 类型。
     * rsvp：用户类型的参与人主动对日程进行回复（包括回复接收、拒绝、待定）。
     */
    @JsonProperty("change_type")
    private String changeType;

    @JsonProperty("user_id_list")
    private UserId[] userIdList;

    @Data
    public static class UserId {

        @JsonProperty("union_id")
        private String unionId;

        @JsonProperty("user_id")
        private String userId;

        @JsonProperty("open_id")
        private String openId;
    }
}

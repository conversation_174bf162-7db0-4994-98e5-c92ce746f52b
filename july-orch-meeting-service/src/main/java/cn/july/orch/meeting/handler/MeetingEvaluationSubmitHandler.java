package cn.july.orch.meeting.handler;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.model.callback.EventCallbackCommand;
import cn.july.orch.meeting.domain.command.MeetingEvaluationSubmitCommand;
import cn.july.orch.meeting.domain.command.MeetingEvaluationSubmitEventCommand;
import cn.july.orch.meeting.domain.dto.MeetingEvaluationDTO;
import cn.july.orch.meeting.enums.EventCallbackEnum;
import cn.july.orch.meeting.service.MeetingEvaluationActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议评价提交回调处理器
 * @date 2025-01-24
 */
@Slf4j
@Component
public class MeetingEvaluationSubmitHandler implements CallbackHandler<MeetingEvaluationSubmitEventCommand> {
    
    @Resource
    private MeetingEvaluationActionService evaluationActionService;
    
    @Override
    public void handle(EventCallbackCommand command) {
        String eventJson = JsonUtils.toJson(command.getEvent());
        MeetingEvaluationSubmitEventCommand submitCommand = JsonUtils.parse(eventJson, MeetingEvaluationSubmitEventCommand.class);
        
        log.info("收到会议评价提交回调，会议ID：{}，评价人：{}", 
            submitCommand.getMeetingId(), submitCommand.getEvaluatorOpenId());
        
        try {
            // 构建评价提交命令
            MeetingEvaluationSubmitCommand submitEvaluationCommand = MeetingEvaluationSubmitCommand.builder()
                .meetingId(submitCommand.getMeetingId())
                .evaluatorOpenId(submitCommand.getEvaluatorOpenId())
                .evaluatorName(submitCommand.getEvaluatorName())
                .evaluationDTO(MeetingEvaluationDTO.builder()
                    .meetingScore(submitCommand.getMeetingScore())
                    .contentScore(submitCommand.getContentScore())
                    .durationScore(submitCommand.getDurationScore())
                    .effectivenessScore(submitCommand.getEffectivenessScore())
                    .suggestions(submitCommand.getSuggestions())
                    .build())
                .build();
            
            // 提交评价
            evaluationActionService.submitEvaluation(submitEvaluationCommand);
            
            log.info("会议评价提交处理成功，会议ID：{}，评价人：{}", 
                submitCommand.getMeetingId(), submitCommand.getEvaluatorOpenId());
        } catch (Exception e) {
            log.error("会议评价提交处理失败，会议ID：{}，评价人：{}", 
                submitCommand.getMeetingId(), submitCommand.getEvaluatorOpenId(), e);
        }
    }
    
    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_EVALUATION_SUBMIT;
    }
} 
package cn.july.orch.meeting.controller;

import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.model.ContactSearchUserDTO;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.domain.dto.ContactSearchUserRespDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;

@Slf4j
@Api(tags = "用户信息")
@RestController
@RequestMapping("/user")
public class UserInfoController {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private UserInfoAssembler userInfoAssembler;

    /**
     * 手机号获取飞书用户信息(方便测试用)
     * 支持离职员工
     * @param telephone
     * @return
     */
    @GetMapping("/getUserInfo")
    public Object getUserInfo(@RequestParam("telephone")String telephone){
        return Arrays.asList(feishuAppClient.getContactService().getOpenId(telephone).getUserList());
    }

    /**
     * 名称模糊查询飞书用户信息
     * 不支持搜索离职员工
     */
    @PostMapping("/searchUser")
    public ContactSearchUserRespDTO searchUser(@RequestBody ContactSearchUserDTO query){
        return userInfoAssembler.resp2DTO(feishuAppClient.getContactService().searchUser(query));
    }
}

package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划列表DTO
 * @date 2025-01-24
 */
@Data
public class MeetingPlanListDTO {

    @ApiModelProperty(value = "会议规划ID")
    private Long id;

    @ApiModelProperty(value = "会议规划名称")
    private String planName;

    @ApiModelProperty(value = "会议规划描述")
    private String planDescription;

    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime plannedStartTime;

    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime plannedEndTime;

    @ApiModelProperty(value = "计划持续时长(分钟)")
    private Integer plannedDuration;

    @ApiModelProperty(value = "会议规划状态")
    private MeetingPlanStatusEnum status;

    @ApiModelProperty(value = "会议标准ID")
    private Long meetingStandardId;

    @ApiModelProperty(value = "会议标准名称")
    private String meetingStandardName;

    @ApiModelProperty(value = "优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "业务会议名称")
    private String businessMeetingName;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "参会人数")
    private Integer attendeeCount;

    @ApiModelProperty(value = "参会人员列表（用户ID列表）")
    private List<String> attendees;

    @ApiModelProperty(value = "参会人员详细信息列表")
    private List<FSUserInfoDTO> attendeeDetails;

    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "会前文档文件ID列表")
    private List<String> preMeetingDocuments;

    @ApiModelProperty(value = "会前文档详细信息列表")
    private List<FileInfoDTO> preMeetingDocumentDetails;
}

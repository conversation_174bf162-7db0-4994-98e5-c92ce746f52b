package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.agg.MeetingEvaluationAgg;
import cn.july.orch.meeting.repository.IMeetingEvaluationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议评价领域服务实现
 * @date 2025-01-24
 */
@Slf4j
@Service
public class MeetingEvaluationDomainServiceImpl implements MeetingEvaluationDomainService {
    
    @Resource
    private IMeetingEvaluationRepository meetingEvaluationRepository;
    
    @Override
    public void saveEvaluation(MeetingEvaluationAgg evaluationAgg) {
        meetingEvaluationRepository.save(evaluationAgg);
    }
    
    @Override
    public MeetingEvaluationAgg findByMeetingIdAndEvaluator(Long meetingId, String evaluatorOpenId) {
        return meetingEvaluationRepository.findByMeetingIdAndEvaluator(meetingId, evaluatorOpenId);
    }
} 
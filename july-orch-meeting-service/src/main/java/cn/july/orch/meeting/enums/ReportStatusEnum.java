package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告状态枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum ReportStatusEnum {

    GENERATING(0, "生成中"),
    GENERATED(1, "已生成"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, ReportStatusEnum> VALUES = new HashMap<>();

    static {
        for (final ReportStatusEnum item : ReportStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ReportStatusEnum of(int code) {
        return VALUES.get(code);
    }
} 
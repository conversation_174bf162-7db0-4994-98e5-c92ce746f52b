package cn.july.orch.meeting.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议评价信息
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEvaluationInfo {
    
    private Long id;
    private Long meetingId;
    private String evaluatorOpenId;
    private String evaluatorName;
    private Integer meetingScore; // 1-5分
    private Integer contentScore; // 1-5分
    private Integer durationScore; // 1-5分
    private Integer effectivenessScore; // 1-5分
    private String suggestions;
    private LocalDateTime evaluationTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Boolean deleted;
} 
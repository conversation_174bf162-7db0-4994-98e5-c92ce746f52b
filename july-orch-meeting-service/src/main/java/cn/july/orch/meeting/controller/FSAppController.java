package cn.july.orch.meeting.controller;

import cn.july.feishu.properties.FeishuProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 飞书应用信息
 *
 * <AUTHOR>
 */
@Api(tags = "飞书应用信息")
@RestController
@RequestMapping("/app")
@RequiredArgsConstructor
public class FSAppController {

    private final FeishuProperties feishuProperties;

    @PostMapping("/id")
    @ApiOperation(value = "获取飞书appId")
    public String id() {
        return feishuProperties.getAppId();
    }
}

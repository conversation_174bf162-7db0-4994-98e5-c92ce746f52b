package cn.july.orch.meeting.service;

import cn.july.core.model.ddd.IdQuery;
import cn.july.orch.meeting.domain.command.TaskCommand;
import cn.july.orch.meeting.domain.dto.TaskDTO;
import cn.july.orch.meeting.feishu.FeishuApiService;
import cn.july.orch.meeting.feishu.req.CreateTaskRequest;
import cn.july.orch.meeting.feishu.req.UpdateTaskRequest;
import com.lark.oapi.service.task.v2.model.CreateTaskRespBody;
import com.lark.oapi.service.task.v2.model.Member;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * <AUTHOR> Assistant
 * @description 任务操作服务
 */
@Slf4j
@Service
public class TaskActionService {

    @Resource
    private TaskDomainService taskDomainService;
    @Resource
    private FeishuApiService feishuApiService;
    @Resource
    private TaskQueryService taskQueryService;

    /**
     * 创建任务
     *
     * @param command 任务命令
     * @return 任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(TaskCommand command) {
        log.info("创建任务，标题：{}", command.getTitle());

        // 1. 先创建本地任务
        Long taskId = taskDomainService.create(
            command.getTitle(),
            command.getDescription(),
            command.getOwnerOpenId(),
            command.getOwnerName(),
            command.getPriority(),
            command.getDueDate(),
            command.getMeetingId()
        );

        try {
            // 2. 创建飞书任务
            Member member = Member.newBuilder()
                    .id(command.getOwnerOpenId())
                    .role("assignee")
                    .type("user")
                    .name(command.getOwnerName())
                    .build();

            CreateTaskRespBody createTaskResp = feishuApiService.createTask(CreateTaskRequest.builder()
                    .summary(command.getTitle())
                    .description(command.getDescription())
                    .dueTime(command.getDueDate())
                    .members(Collections.singletonList(member))
                    .build());

            // 3. 获取飞书任务的guid并存储到本地任务
            if (createTaskResp != null && createTaskResp.getTask() != null) {
                String feishuTaskGuid = createTaskResp.getTask().getGuid();
                if (feishuTaskGuid != null) {
                    taskDomainService.updateFeishuTaskId(taskId, feishuTaskGuid);
                    log.info("任务创建成功，本地任务ID：{}，飞书任务GUID：{}", taskId, feishuTaskGuid);
                } else {
                    log.warn("飞书任务创建成功但未返回GUID，本地任务ID：{}", taskId);
                }
            } else {
                log.warn("飞书任务创建响应异常，本地任务ID：{}", taskId);
            }

        } catch (Exception e) {
            log.error("创建飞书任务失败，本地任务ID：{}，错误：{}", taskId, e.getMessage(), e);
            // 飞书任务创建失败不影响本地任务创建，只记录日志
        }

        return taskId;
    }

    /**
     * 更新任务
     *
     * @param command 任务命令
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskCommand command) {
        log.info("更新任务，ID：{}，标题：{}", command.getId(), command.getTitle());

        taskDomainService.update(
                command.getId(),
                command.getTitle(),
                command.getDescription(),
                command.getOwnerOpenId(),
                command.getOwnerName(),
                command.getPriority(),
                command.getDueDate(),
                command.getMeetingId()
        );
    }

    /**
     * 删除任务
     *
     * @param idQuery ID查询
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(IdQuery idQuery) {
        log.info("删除任务，ID：{}", idQuery.getId());
        TaskDTO taskDTO = taskQueryService.detail(idQuery);
        taskDomainService.delete(idQuery.getId());
        feishuApiService.deleteTask(taskDTO.getFeishuTaskId());
    }

    /**
     * 完成任务
     *
     * @param idQuery ID查询
     */
    @Transactional(rollbackFor = Exception.class)
    public void complete(IdQuery idQuery) {
        log.info("完成任务，ID：{}", idQuery.getId());
        taskDomainService.complete(idQuery.getId());
        TaskDTO taskDTO = taskQueryService.detail(idQuery);
        feishuApiService.patchTask(UpdateTaskRequest.builder()
            .taskGuid(taskDTO.getFeishuTaskId())
            .completedTime(LocalDateTime.now())
            .build());
    }

    /**
     * 开始任务
     *
     * @param idQuery ID查询
     */
    @Transactional(rollbackFor = Exception.class)
    public void start(IdQuery idQuery) {
        log.info("开始任务，ID：{}", idQuery.getId());
        taskDomainService.start(idQuery.getId());
    }

    /**
     * 更新飞书任务ID
     *
     * @param taskId 任务ID
     * @param feishuTaskId 飞书任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFeishuTaskId(Long taskId, String feishuTaskId) {
        log.info("更新飞书任务ID，任务ID：{}，飞书任务ID：{}", taskId, feishuTaskId);
        taskDomainService.updateFeishuTaskId(taskId, feishuTaskId);
    }

    /**
     * 处理超期任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void processOverdueTasks() {
        log.info("处理超期任务");
        taskDomainService.processOverdueTasks();
    }
}

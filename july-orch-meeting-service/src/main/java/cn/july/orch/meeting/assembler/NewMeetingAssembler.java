package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.NewMeetingCreateCommand;
import cn.july.orch.meeting.domain.command.NewMeetingUpdateCommand;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingListDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议装配器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring")
public interface NewMeetingAssembler {

    /**
     * Command转Entity
     */
    NewMeeting toEntity(NewMeetingCreateCommand command);

    /**
     * Command转Entity（更新）
     */
    @Mapping(target = "id", source = "id")
    NewMeeting toEntity(NewMeetingUpdateCommand command);

    /**
     * Entity转DTO
     */
    NewMeetingDTO toDTO(NewMeeting entity);

    /**
     * Entity列表转DTO列表
     */
    List<NewMeetingDTO> toDTOList(List<NewMeeting> entityList);

    /**
     * Entity转分页列表DTO
     */
    NewMeetingListDTO toListDTO(NewMeeting entity);

    /**
     * Entity列表转分页列表DTO列表
     */
    List<NewMeetingListDTO> toListDTOList(List<NewMeeting> entityList);
} 
package cn.july.orch.meeting.service;

import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.domain.dto.SendMeetingEvaluationSurveyDTO;
import cn.july.orch.meeting.domain.dto.card.SendMeetingNotificationDTO;
import cn.july.orch.meeting.feishu.FeishuApiService;
import cn.july.orch.meeting.properties.MeetingSeverProperties;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CardSendActionService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private MeetingSeverProperties meetingSeverProperties;

    @Resource
    private FeishuApiService feishuApiService;

//    /**
//     * 签到码发送
//     */
//    public CreateMessageRespBody sendSignInCode(SendSignInCodeDTO dto) {
//        Map<String, Object> templateVariable = new HashMap<>();
//        templateVariable.put("meetingType",dto.getMeetingType());
//        templateVariable.put("meetingName",dto.getMeetingName());
//        templateVariable.put("meetingTime",dto.getMeetingTime());
//        templateVariable.put("signInCode",dto.getSignInCode());
//        templateVariable.put("meetingUrl",dto.getMeetingUrl());
//        String templateId = meetingSeverProperties.getCardSend().getTemplate().getSignInCodeId();
//        return feishuAppClient.getRobotService().sendCard(dto.getOpenId(), templateId,templateVariable);
//    }
//
//    /**
//     * 签到提醒
//     */
//    public CreateMessageRespBody sendSignInRemind(SendSignInRemindDTO dto) {
//        Map<String, Object> templateVariable = new HashMap<>();
//        templateVariable.put("meetingType",dto.getMeetingType());
//        templateVariable.put("meetingName",dto.getMeetingName());
//        templateVariable.put("signTime",dto.getSignTime());
//        templateVariable.put("signUrl",dto.getSignUrl());
//        String templateId = meetingSeverProperties.getCardSend().getTemplate().getSignInRemindId();
//        return feishuAppClient.getRobotService().sendCard(dto.getOpenId(), templateId,templateVariable);
//    }
//
//    /**
//     * 会议记录上传
//     */
//    public CreateMessageRespBody sendUploadMeetingRecord(SendUploadMeetingRecordDTO dto) {
//        Map<String, Object> templateVariable = new HashMap<>();
//        templateVariable.put("meetingType",dto.getMeetingType());
//        templateVariable.put("meetingName",dto.getMeetingName());
//        templateVariable.put("meetingTime",dto.getMeetingTime());
//        templateVariable.put("uploadUrl",dto.getUploadUrl());
//        String templateId = meetingSeverProperties.getCardSend().getTemplate().getUploadMeetingRecordId();
//        return feishuAppClient.getRobotService().sendCard(dto.getOpenId(), templateId,templateVariable);
//    }
//
//    /**
//     * 会议评价
//     */
//    public CreateMessageRespBody sendMeetingEvaluation(SendMeetingEvaluationDTO dto) {
//        Map<String, Object> templateVariable = new HashMap<>();
//        templateVariable.put("meetingType",dto.getMeetingType());
//        templateVariable.put("meetingName",dto.getMeetingName());
//        templateVariable.put("meetingTime",dto.getMeetingTime());
//        templateVariable.put("evaluationUrl",dto.getEvaluationUrl());
//        String templateId = meetingSeverProperties.getCardSend().getTemplate().getMeetingEvaluationId();
//        return feishuAppClient.getRobotService().sendCard(dto.getOpenId(), templateId,templateVariable);
//    }
//
//    /**
//     * 会议妙记分享
//     */
//    public CreateMessageRespBody sendShareMeetingVideo(SendShareMeetingVideoDTO dto) {
//        Map<String, Object> templateVariable = new HashMap<>();
//        templateVariable.put("meetingType",dto.getMeetingType());
//        templateVariable.put("meetingName",dto.getMeetingName());
//        templateVariable.put("meetingTime",dto.getMeetingTime());
//        templateVariable.put("videoUrl",dto.getVideoUrl());
//        String templateId = meetingSeverProperties.getCardSend().getTemplate().getShareMeetingVideoId();
//        return feishuAppClient.getRobotService().sendCard(dto.getOpenId(), templateId,templateVariable);
//    }

    /**
     * 会议提前通知
     */
    public CreateMessageRespBody sendMeetingNotification(SendMeetingNotificationDTO dto) {
        try {
            // 构建飞书卡片消息
            String cardContent = buildMeetingNotificationCardContent(dto);

            // 发送消息
            CreateMessageReq req = CreateMessageReq.newBuilder()
                    .receiveIdType("open_id")
                    .createMessageReqBody(CreateMessageReqBody.newBuilder()
                            .receiveId(dto.getOpenId())
                            .msgType("interactive")
                            .content(cardContent)
                            .build())
                    .build();

            CreateMessageRespBody response = feishuApiService.sendMessage(req);
            log.info("会议提前通知卡片发送成功，接收人: {}, 会议名称: {}", dto.getOpenId(), dto.getMeetingName());
            return response;

        } catch (Exception e) {
            log.error("会议提前通知卡片发送失败，接收人: {}, 会议名称: {}", dto.getOpenId(), dto.getMeetingName(), e);
            throw new RuntimeException("会议提前通知卡片发送失败", e);
        }
    }

    /**
     * 构建会议提前通知飞书卡片内容
     */
    private String buildMeetingNotificationCardContent(SendMeetingNotificationDTO dto) {
        // 根据优先级选择卡片颜色主题
        String themeColor = getMeetingCardThemeColor(dto.getPriorityLevel());
        String priorityIcon = getMeetingPriorityIcon(dto.getPriorityLevel());

        // 处理参会人员列表
        String attendeesText = dto.getAttendees() != null && !dto.getAttendees().isEmpty()
            ? String.join("、", dto.getAttendees())
            : "暂无";

        String cardJson = String.format(
            "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"template\": \"%s\",\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"%s 会议提前通知\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"elements\": [\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议名称**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议类型**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议时间**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议时长**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议地点**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**优先级**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**参会人员**\\n%s\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**会议描述**\\n%s\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"hr\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**通知类型**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**提前时间**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"button\",\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"查看会议详情\"\n" +
            "          },\n" +
            "          \"type\": \"primary\",\n" +
            "          \"value\": \"{\\\"action\\\":\\\"view_meeting_detail\\\",\\\"meeting_url\\\":\\\"%s\\\"}\",\n" +
            "          \"url\": \"%s\"\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}",
            themeColor,
            priorityIcon,
            escapeJsonString(dto.getMeetingName()),
            escapeJsonString(dto.getMeetingType()),
            escapeJsonString(dto.getMeetingTime()),
            escapeJsonString(dto.getMeetingDuration()),
            escapeJsonString(dto.getMeetingLocation() != null ? dto.getMeetingLocation() : "待定"),
            escapeJsonString(dto.getPriorityLevel()),
            escapeJsonString(attendeesText),
            escapeJsonString(dto.getMeetingDescription() != null ? dto.getMeetingDescription() : "暂无描述"),
            escapeJsonString(dto.getNoticeType()),
            escapeJsonString(dto.getNoticeTime()),
            dto.getMeetingUrl() != null ? dto.getMeetingUrl() : "#",
            dto.getMeetingUrl() != null ? dto.getMeetingUrl() : "#"
        );

        log.debug("会议通知卡片JSON: {}", cardJson);
        return cardJson;
    }

    /**
     * 根据优先级获取会议卡片主题颜色
     */
    private String getMeetingCardThemeColor(String priorityLevel) {
        if (priorityLevel == null) {
            return "blue";
        }

        switch (priorityLevel.toLowerCase()) {
            case "高":
            case "紧急":
                return "red";
            case "中":
            case "普通":
                return "orange";
            case "低":
                return "blue";
            default:
                return "blue";
        }
    }

    /**
     * 根据优先级获取图标
     */
    private String getMeetingPriorityIcon(String priorityLevel) {
        if (priorityLevel == null) {
            return "📅";
        }

        switch (priorityLevel.toLowerCase()) {
            case "高":
            case "紧急":
                return "🔴";
            case "中":
            case "普通":
                return "🟡";
            case "低":
                return "🟢";
            default:
                return "📅";
        }
    }

    /**
     * 发送会议评价调查卡片
     */
    public CreateMessageRespBody sendMeetingEvaluationSurvey(SendMeetingEvaluationSurveyDTO dto) {
        try {
            // 构建飞书卡片消息
            String cardContent = buildMeetingEvaluationSurveyCardContent(dto);

            // 发送消息
            CreateMessageReq req = CreateMessageReq.newBuilder()
                    .receiveIdType("open_id")
                    .createMessageReqBody(CreateMessageReqBody.newBuilder()
                            .receiveId(dto.getOpenId())
                            .msgType("interactive")
                            .content(cardContent)
                            .build())
                    .build();

            CreateMessageRespBody response = feishuApiService.sendMessage(req);
            log.info("会议评价调查卡片发送成功，接收人: {}, 会议名称: {}", dto.getOpenId(), dto.getMeetingName());
            return response;

        } catch (Exception e) {
            log.error("会议评价调查卡片发送失败，接收人: {}, 会议名称: {}", dto.getOpenId(), dto.getMeetingName(), e);
            throw new RuntimeException("会议评价调查卡片发送失败", e);
        }
    }

    /**
     * 构建会议评价调查飞书卡片内容
     */
    private String buildMeetingEvaluationSurveyCardContent(SendMeetingEvaluationSurveyDTO dto) {
        String cardJson = String.format(
            "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"template\": \"blue\",\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"📝 会议评价调查\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"elements\": [\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议名称**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议类型**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**会议时间**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**评价人**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"hr\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**请对本次会议进行评价：**\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**1. 会议整体评分**\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"select_static\",\n" +
            "          \"name\": \"meeting_score\",\n" +
            "          \"placeholder\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"请选择评分\"\n" +
            "          },\n" +
            "          \"options\": [\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"1分 - 非常不满意\"\n" +
            "              },\n" +
            "              \"value\": \"1\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"2分 - 不满意\"\n" +
            "              },\n" +
            "              \"value\": \"2\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"3分 - 一般\"\n" +
            "              },\n" +
            "              \"value\": \"3\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"4分 - 满意\"\n" +
            "              },\n" +
            "              \"value\": \"4\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"5分 - 非常满意\"\n" +
            "              },\n" +
            "              \"value\": \"5\"\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**2. 会议内容评分**\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"select_static\",\n" +
            "          \"name\": \"content_score\",\n" +
            "          \"placeholder\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"请选择评分\"\n" +
            "          },\n" +
            "          \"options\": [\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"1分 - 非常不满意\"\n" +
            "              },\n" +
            "              \"value\": \"1\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"2分 - 不满意\"\n" +
            "              },\n" +
            "              \"value\": \"2\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"3分 - 一般\"\n" +
            "              },\n" +
            "              \"value\": \"3\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"4分 - 满意\"\n" +
            "              },\n" +
            "              \"value\": \"4\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"5分 - 非常满意\"\n" +
            "              },\n" +
            "              \"value\": \"5\"\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**3. 会议时长评分**\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"select_static\",\n" +
            "          \"name\": \"duration_score\",\n" +
            "          \"placeholder\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"请选择评分\"\n" +
            "          },\n" +
            "          \"options\": [\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"1分 - 太短\"\n" +
            "              },\n" +
            "              \"value\": \"1\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"2分 - 偏短\"\n" +
            "              },\n" +
            "              \"value\": \"2\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"3分 - 合适\"\n" +
            "              },\n" +
            "              \"value\": \"3\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"4分 - 偏长\"\n" +
            "              },\n" +
            "              \"value\": \"4\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"5分 - 太长\"\n" +
            "              },\n" +
            "              \"value\": \"5\"\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**4. 会议效果评分**\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"select_static\",\n" +
            "          \"name\": \"effectiveness_score\",\n" +
            "          \"placeholder\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"请选择评分\"\n" +
            "          },\n" +
            "          \"options\": [\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"1分 - 未达到预期\"\n" +
            "              },\n" +
            "              \"value\": \"1\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"2分 - 部分达到预期\"\n" +
            "              },\n" +
            "              \"value\": \"2\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"3分 - 达到预期\"\n" +
            "              },\n" +
            "              \"value\": \"3\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"4分 - 超出预期\"\n" +
            "              },\n" +
            "              \"value\": \"4\"\n" +
            "            },\n" +
            "            {\n" +
            "              \"text\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"5分 - 远超预期\"\n" +
            "              },\n" +
            "              \"value\": \"5\"\n" +
            "            }\n" +
            "          ]\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**5. 改进建议**\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"input\",\n" +
            "          \"name\": \"suggestions\",\n" +
            "          \"required\": false,\n" +
            "          \"placeholder\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"请输入您的改进建议（最多500字）\"\n" +
            "          },\n" +
            "          \"max_length\": 500\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"hr\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"action\",\n" +
            "      \"actions\": [\n" +
            "        {\n" +
            "          \"tag\": \"button\",\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"plain_text\",\n" +
            "            \"content\": \"提交评价\"\n" +
            "          },\n" +
            "          \"type\": \"primary\",\n" +
            "          \"value\": \"{\\\"action\\\":\\\"submit_meeting_evaluation\\\",\\\"meeting_id\\\":\\\"%d\\\",\\\"evaluator_open_id\\\":\\\"%s\\\"}\"\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}",
            escapeJsonString(dto.getMeetingName()),
            escapeJsonString(dto.getMeetingType()),
            escapeJsonString(dto.getMeetingTime()),
            escapeJsonString(dto.getEvaluatorName()),
            dto.getMeetingId(),
            dto.getOpenId()
        );

        log.debug("会议评价调查卡片JSON: {}", cardJson);
        return cardJson;
    }

    /**
     * JSON字符串转义
     */
    private String escapeJsonString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    /**
     * 发送测试交互卡片
     */
    public CreateMessageRespBody sendTestInteractiveCard(String openId) {
        try {
            // 构建测试交互卡片内容
            String cardContent = buildTestInteractiveCardContent();

            // 发送消息
            CreateMessageReq req = CreateMessageReq.newBuilder()
                    .receiveIdType("open_id")
                    .createMessageReqBody(CreateMessageReqBody.newBuilder()
                            .receiveId(openId)
                            .msgType("interactive")
                            .content(cardContent)
                            .build())
                    .build();

            CreateMessageRespBody response = feishuApiService.sendMessage(req);
            log.info("测试交互卡片发送成功，接收人: {}", openId);
            return response;

        } catch (Exception e) {
            log.error("测试交互卡片发送失败，接收人: {}", openId, e);
            throw new RuntimeException("测试交互卡片发送失败", e);
        }
    }

    /**
     * 构建测试交互卡片内容
     */
    private String buildTestInteractiveCardContent() {
        String currentTime = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        String cardJson = "{\n" +
                "  \"config\": {\n" +
                "    \"wide_screen_mode\": true\n" +
                "  },\n" +
                "  \"header\": {\n" +
                "    \"template\": \"blue\",\n" +
                "    \"title\": {\n" +
                "      \"tag\": \"plain_text\",\n" +
                "      \"content\": \"🧪 卡片交互测试\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"elements\": [\n" +
                "    {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"text\": {\n" +
                "        \"tag\": \"lark_md\",\n" +
                "        \"content\": \"**测试说明**\\n这是一个测试卡片，用于验证卡片交互功能。点击下方按钮后，卡片将在5秒后自动禁用。\"\n" +
                "      }\n" +
                "    },\n" +
                "    {\n" +
                "      \"tag\": \"hr\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"tag\": \"div\",\n" +
                "      \"fields\": [\n" +
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**测试时间**\\n" + currentTime + "\"\n" +
                "          }\n" +
                "        },\n" +
                "        {\n" +
                "          \"is_short\": true,\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"lark_md\",\n" +
                "            \"content\": \"**功能**\\n卡片交互回调测试\"\n" +
                "          }\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"tag\": \"action\",\n" +
                "      \"actions\": [\n" +
                "        {\n" +
                "          \"tag\": \"button\",\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"plain_text\",\n" +
                "            \"content\": \"测试按钮1\"\n" +
                "          },\n" +
                "          \"type\": \"primary\",\n" +
                "          \"value\": \"{\\\"action\\\":\\\"test_action_1\\\",\\\"data\\\":\\\"test_data_1\\\"}\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"tag\": \"button\",\n" +
                "          \"text\": {\n" +
                "            \"tag\": \"plain_text\",\n" +
                "            \"content\": \"测试按钮2\"\n" +
                "          },\n" +
                "          \"type\": \"default\",\n" +
                "          \"value\": \"{\\\"action\\\":\\\"test_action_2\\\",\\\"data\\\":\\\"test_data_2\\\"}\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        log.debug("测试交互卡片JSON: {}", cardJson);
        return cardJson;
    }

}

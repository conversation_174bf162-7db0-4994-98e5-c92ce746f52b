package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.assembler.MeetingStandardConverter;
import cn.july.orch.meeting.domain.dto.AttendeeStatistics;
import cn.july.orch.meeting.domain.entity.MeetingStandard;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.mapper.MeetingPlanMapper;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议标准仓储实现
 * @date 2025-01-24
 */
@Repository
public class MeetingStandardRepositoryImpl implements IMeetingStandardRepository {

    @Resource
    private MeetingStandardMapper meetingStandardMapper;
    @Resource
    private MeetingPlanMapper meetingPlanMapper;
    @Resource
    private MeetingStandardConverter meetingStandardConverter;

    @Override
    public MeetingStandard findById(Long id) {
        MeetingStandardPO po = meetingStandardMapper.selectById(id);
        return po != null ? meetingStandardConverter.toEntity(po) : null;
    }

    @Override
    public List<MeetingStandard> findAllEnabled() {
        LambdaQueryWrapper<MeetingStandardPO> wrapper = Wrappers.lambdaQuery(MeetingStandardPO.class)
            .eq(MeetingStandardPO::getIsEnabled, 1);
        
        List<MeetingStandardPO> pos = meetingStandardMapper.selectList(wrapper);
        return pos.stream()
            .map(meetingStandardConverter::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public void save(MeetingStandard meetingStandard) {
        MeetingStandardPO po = meetingStandardConverter.toPO(meetingStandard);
        meetingStandardMapper.insert(po);
        meetingStandard.setId(po.getId());
    }

    @Override
    public void update(MeetingStandard meetingStandard) {
        MeetingStandardPO po = meetingStandardConverter.toPO(meetingStandard);
        meetingStandardMapper.updateById(po);
    }

    @Override
    public void deleteById(Long id) {
        meetingStandardMapper.deleteById(id);
    }

    @Override
    public boolean existsByName(String standardName, Long excludeId) {
        LambdaQueryWrapper<MeetingStandardPO> wrapper = Wrappers.lambdaQuery(MeetingStandardPO.class)
            .eq(MeetingStandardPO::getStandardName, standardName);
        
        if (excludeId != null) {
            wrapper.ne(MeetingStandardPO::getId, excludeId);
        }
        
        return meetingStandardMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isUsedByMeetingPlan(Long standardId) {
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .eq(MeetingPlanPO::getMeetingStandardId, standardId);
        return meetingPlanMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<Long> findMeetingPlanIdsByStandardId(Long standardId) {
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .eq(MeetingPlanPO::getMeetingStandardId, standardId)
            .select(MeetingPlanPO::getId);
        
        List<MeetingPlanPO> pos = meetingPlanMapper.selectList(wrapper);
        return pos.stream()
            .map(MeetingPlanPO::getId)
            .collect(Collectors.toList());
    }

    @Override
    public AttendeeStatistics getAttendeeStatisticsByStandardId(Long standardId) {
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .eq(MeetingPlanPO::getMeetingStandardId, standardId)
            .select(MeetingPlanPO::getAttendees);
        
        List<MeetingPlanPO> pos = meetingPlanMapper.selectList(wrapper);
        
        if (pos.isEmpty()) {
            return AttendeeStatistics.builder()
                .totalPlans(0)
                .minAttendees(0)
                .maxAttendees(0)
                .avgAttendees(0.0)
                .distribution(AttendeeStatistics.AttendeeDistribution.builder()
                    .smallMeetings(0)
                    .mediumMeetings(0)
                    .largeMeetings(0)
                    .extraLargeMeetings(0)
                    .build())
                .build();
        }
        
        // 统计参会人数
        List<Integer> attendeeCounts = pos.stream()
            .map(po -> po.getAttendees() != null ? po.getAttendees().size() : 0)
            .filter(count -> count > 0)
            .collect(Collectors.toList());
        
        if (attendeeCounts.isEmpty()) {
            return AttendeeStatistics.builder()
                .totalPlans(pos.size())
                .minAttendees(0)
                .maxAttendees(0)
                .avgAttendees(0.0)
                .distribution(AttendeeStatistics.AttendeeDistribution.builder()
                    .smallMeetings(0)
                    .mediumMeetings(0)
                    .largeMeetings(0)
                    .extraLargeMeetings(0)
                    .build())
                .build();
        }
        
        // 计算统计数据
        int minAttendees = attendeeCounts.stream().mapToInt(Integer::intValue).min().orElse(0);
        int maxAttendees = attendeeCounts.stream().mapToInt(Integer::intValue).max().orElse(0);
        double avgAttendees = attendeeCounts.stream().mapToInt(Integer::intValue).average().orElse(0.0);
        
        // 统计分布
        int smallMeetings = (int) attendeeCounts.stream().filter(count -> count >= 1 && count <= 5).count();
        int mediumMeetings = (int) attendeeCounts.stream().filter(count -> count >= 6 && count <= 15).count();
        int largeMeetings = (int) attendeeCounts.stream().filter(count -> count >= 16 && count <= 30).count();
        int extraLargeMeetings = (int) attendeeCounts.stream().filter(count -> count > 30).count();
        
        return AttendeeStatistics.builder()
            .totalPlans(pos.size())
            .minAttendees(minAttendees)
            .maxAttendees(maxAttendees)
            .avgAttendees(avgAttendees)
            .distribution(AttendeeStatistics.AttendeeDistribution.builder()
                .smallMeetings(smallMeetings)
                .mediumMeetings(mediumMeetings)
                .largeMeetings(largeMeetings)
                .extraLargeMeetings(extraLargeMeetings)
                .build())
            .build();
    }
}

package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务通知类型枚举
 */
@Getter
public enum TaskNotificationTypeEnum {

    TASK_DETAIL_CHANGED(1, "任务详情发生变化"),
    TASK_COLLABORATOR_CHANGED(2, "任务协作者发生变化"),
    TASK_FOLLOWER_CHANGED(3, "任务关注者发生变化"),
    TASK_REMINDER_CHANGED(4, "任务提醒时间发生变化"),
    TASK_COMPLETED(5, "任务完成"),
    TASK_UNCOMPLETED(6, "任务取消完成"),
    TASK_DELETED(7, "任务删除");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String description;

    TaskNotificationTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, TaskNotificationTypeEnum> VALUES = new HashMap<>();

    static {
        for (final TaskNotificationTypeEnum item : TaskNotificationTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TaskNotificationTypeEnum of(Integer code) {
        return VALUES.get(code);
    }
}

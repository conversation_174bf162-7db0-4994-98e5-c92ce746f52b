package cn.july.orch.meeting.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Assistant
 * @description 任务督办飞书卡片DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendTaskSupervisionDTO {

    /**
     * 送达人员OpenId
     */
    private String openId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务标题
     */
    private String taskTitle;

    /**
     * 任务描述
     */
    private String taskDescription;

    /**
     * 负责人姓名
     */
    private String ownerName;

    /**
     * 优先级名称
     */
    private String priorityName;

    /**
     * 优先级颜色（用于卡片样式）
     */
    private String priorityColor;

    /**
     * 截止时间（格式化后的字符串）
     */
    private String dueDate;

    /**
     * 剩余时间描述
     */
    private String remainingTime;

    /**
     * 紧急程度描述
     */
    private String urgencyLevel;

    /**
     * 任务详情链接
     */
    private String taskDetailUrl;

    /**
     * 会议名称（如果关联会议）
     */
    private String meetingName;

    /**
     * 督办类型（自动督办/手动督办）
     */
    private String supervisionType;

    /**
     * 督办时间
     */
    private String supervisionTime;
}

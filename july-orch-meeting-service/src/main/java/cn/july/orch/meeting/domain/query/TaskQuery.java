package cn.july.orch.meeting.domain.query;

import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("任务查询条件")
public class TaskQuery extends PageSortQuery {

    @ApiModelProperty("任务标题（模糊查询）")
    private String title;

    @ApiModelProperty("负责人OpenID")
    private String ownerOpenId;

    @ApiModelProperty("负责人名称（模糊查询）")
    private String ownerName;

    @ApiModelProperty("优先级")
    private TaskPriorityEnum priority;

    @ApiModelProperty("任务状态")
    private TaskStatusEnum status;

    @ApiModelProperty("关联的会议ID")
    private Long meetingId;

    @ApiModelProperty("截止时间开始")
    private LocalDateTime dueDateStart;

    @ApiModelProperty("截止时间结束")
    private LocalDateTime dueDateEnd;

    @ApiModelProperty("创建时间开始")
    private LocalDateTime createTimeStart;

    @ApiModelProperty("创建时间结束")
    private LocalDateTime createTimeEnd;

    @ApiModelProperty("是否只查询超期任务")
    private Boolean onlyOverdue;
}

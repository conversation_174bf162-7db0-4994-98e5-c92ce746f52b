package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 会议标准Mapper
 * @date 2025-01-24
 */
@Mapper
public interface MeetingStandardMapper extends BaseMapper<MeetingStandardPO> {

    /**
     * 根据标准名称统计数量（排除指定ID）
     */
    int countByName(@Param("standardName") String standardName, @Param("excludeId") Long excludeId);

    /**
     * 统计会议标准被会议规划使用的次数
     */
    int countUsedByMeetingPlan(@Param("standardId") Long standardId);
}

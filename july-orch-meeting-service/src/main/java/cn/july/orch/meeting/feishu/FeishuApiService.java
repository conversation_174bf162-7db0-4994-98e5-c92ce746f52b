package cn.july.orch.meeting.feishu;

import cn.july.orch.meeting.feishu.req.CreateTaskRequest;
import cn.july.orch.meeting.feishu.req.UpdateTaskRequest;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserReq;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserReqBody;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserRespBody;
import com.lark.oapi.service.im.v1.model.*;
import com.lark.oapi.service.task.v2.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @description 飞书接口服务
 * @date 2025-06-03
 */
@Slf4j
@Service
public class FeishuApiService {

    @Resource
    private FeishuBaseRequestHolder feishuBaseRequestHolder;

    /**
     * 发送消息
     */
    public CreateMessageRespBody sendMessage(CreateMessageReq req) {
        return FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            req,
            (client, param) -> client.im().message().create(param)
        );
    }

    /**
     * 更新消息卡片
     */
    public UpdateMessageRespBody updateMessage(String messageId, String cardContent) {
        UpdateMessageReq req = UpdateMessageReq.newBuilder()
                .messageId(messageId)
                .updateMessageReqBody(UpdateMessageReqBody.newBuilder()
                        .content(cardContent)
                        .build())
                .build();

        return FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            req,
            (client, param) -> client.im().message().update(param)
        );
    }

    /**
     * 创建任务
     */
    public CreateTaskRespBody createTask(CreateTaskRequest req) {
        CreateTaskReq createTaskReq = CreateTaskReq.newBuilder()
            .userIdType("open_id")
            .inputTask(InputTask.newBuilder()
                .summary(req.getSummary())
                .description(req.getDescription())
                .due(req.getDueTime() != null ? Due.newBuilder().timestamp(String.valueOf(req.getDueTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli())).build() : null)
                .extra(req.getExtra())
                .members(req.getMembers().toArray(new Member[0]))
                .build()).build();
        return FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            req,
            (client, param) -> client.task().v2().task().create(createTaskReq)
        );
    }

    /**
     * 更新任务
     */
    public PatchTaskRespBody patchTask(UpdateTaskRequest req) {
        PatchTaskReq patchTaskReq = PatchTaskReq.newBuilder()
            .taskGuid(req.getTaskGuid())
            .userIdType("open_id")
            .patchTaskReqBody(PatchTaskReqBody.newBuilder()
                .task(InputTask.newBuilder()
                    .summary(req.getSummary())
                    .description(req.getDescription())
                    .start(req.getStartTime() != null ? Start.newBuilder().timestamp(String.valueOf(req.getStartTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli())).build() : null)
                    .completedAt(req.getCompletedTime() != null ? String.valueOf(req.getCompletedTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli()) : null)
                    .due(req.getEndTime() != null ? Due.newBuilder().timestamp(String.valueOf(req.getEndTime().atZone(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli())).build() : null)
                    .extra(req.getExtra())
                    .members(req.getMembers() != null ? req.getMembers().toArray(new Member[0]) : null)
                    .build())
                .updateFields(req.getUpdateFields())
                .build()
            )
            .build();
        return FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            req,
            (client, param) -> client.task().v2().task().patch(patchTaskReq)
        );
    }

    /**
     * 查询任务
     */
    public GetTaskRespBody getTask(String taskGuid) {
        GetTaskReq getTaskReq = GetTaskReq.newBuilder()
            .taskGuid(taskGuid)
            .userIdType("open_id")
            .build();
        return FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            getTaskReq,
            (client, param) -> client.task().v2().task().get(getTaskReq)
        );
    }

    /**
     * 删除任务
     */
    public void deleteTask(String taskGuid) {
        FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            taskGuid,
            (client, param) -> client.task().v2().task().delete(DeleteTaskReq.newBuilder()
                .taskGuid(taskGuid)
                .build())
        );
    }

    /**
     * 根据手机号查询用户信息
     */
    public BatchGetIdUserRespBody getUserByMobile(String mobile) {
        return FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            mobile,
            (client, param) -> client.contact().v3().user().batchGetId(BatchGetIdUserReq.newBuilder()
                .userIdType("open_id")
                .batchGetIdUserReqBody(BatchGetIdUserReqBody.newBuilder()
                    .mobiles(new String[]{mobile})
                    .build())
                .build()));
    }

    /**
     * 创建任务列表
     */
    public CreateTasklistRespBody createTaskList(String taskListName) {
        CreateTasklistReq createTasklistReq = CreateTasklistReq.newBuilder()
            .userIdType("open_id")
            .inputTasklist(InputTasklist.newBuilder()
                .name(taskListName)
                .build())
            .build();
        return FeishuInvokeUtil.invoke(
            feishuBaseRequestHolder.get(),
            createTasklistReq,
            (client, param) -> client.task().v2().tasklist().create(createTasklistReq)
        );
    }
}

package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 是否重复会议枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum IsRecurringEnum {

    NO(0, "否"),
    YES(1, "是");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, IsRecurringEnum> VALUES = new HashMap<>();

    static {
        for (final IsRecurringEnum item : IsRecurringEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static IsRecurringEnum of(int code) {
        return VALUES.get(code);
    }
}

package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务同步服务 - 处理飞书任务与本地任务的同步
 */
@Slf4j
@Service
public class TaskSyncService {

    @Resource
    private TaskDomainService taskDomainService;

    /**
     * 从飞书任务创建本地任务
     *
     * @param feishuTaskId 飞书任务ID
     * @param title 任务标题
     * @param description 任务描述
     * @param ownerOpenId 负责人OpenID
     * @param ownerName 负责人名称
     * @param priority 优先级
     * @param dueDate 截止时间
     * @param meetingId 关联的会议ID
     * @param isCompleted 是否已完成
     * @return 本地任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createFromFeishuTask(String feishuTaskId, String title, String description,
                                   String ownerOpenId, String ownerName, TaskPriorityEnum priority,
                                   LocalDateTime dueDate, Long meetingId, boolean isCompleted) {
        log.info("从飞书任务创建本地任务，飞书任务ID：{}", feishuTaskId);

        // 检查是否已存在
        TaskAgg existingTask = taskDomainService.findByFeishuTaskId(feishuTaskId);
        if (existingTask != null) {
            log.warn("飞书任务已存在对应的本地任务，飞书任务ID：{}，本地任务ID：{}",
                    feishuTaskId, existingTask.getInfo().getId());
            return existingTask.getInfo().getId();
        }

        // 创建新任务（使用系统用户作为操作人）
        Long taskId = taskDomainService.create(title, description, ownerOpenId, ownerName,
                                             priority, dueDate, meetingId,
                                             "system", "系统");

        // 更新飞书任务ID（使用系统用户作为操作人）
        TaskAgg taskAgg = taskDomainService.findById(taskId);
        if (taskAgg != null) {
            taskAgg.updateFeishuTaskId(feishuTaskId, "system", "系统");
            taskDomainService.update(taskAgg);
        }

        // 如果飞书任务已完成，同步状态（使用系统用户作为操作人）
        if (isCompleted) {
            TaskAgg completedTaskAgg = taskDomainService.findById(taskId);
            if (completedTaskAgg != null) {
                completedTaskAgg.complete("system", "系统");
                taskDomainService.update(completedTaskAgg);
            }
        }

        log.info("从飞书任务创建本地任务成功，飞书任务ID：{}，本地任务ID：{}", feishuTaskId, taskId);
        return taskId;
    }

    /**
     * 同步飞书任务状态到本地任务
     *
     * @param feishuTaskId 飞书任务ID
     * @param isCompleted 是否已完成
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncTaskStatus(String feishuTaskId, boolean isCompleted) {
        log.info("同步飞书任务状态，飞书任务ID：{}，是否完成：{}", feishuTaskId, isCompleted);

        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        if (taskAgg == null) {
            log.warn("未找到对应的本地任务，飞书任务ID：{}", feishuTaskId);
            return;
        }

        TaskStatusEnum currentStatus = taskAgg.getInfo().getStatus();

        if (isCompleted && currentStatus != TaskStatusEnum.COMPLETED) {
            // 使用系统用户作为操作人完成任务
            taskAgg.complete("system", "系统");
            taskDomainService.update(taskAgg);
            log.info("任务状态同步为已完成，飞书任务ID：{}", feishuTaskId);
        } else if (!isCompleted && currentStatus == TaskStatusEnum.COMPLETED) {
            // 使用系统用户作为操作人重新开始任务
            taskAgg.start("system", "系统");
            taskDomainService.update(taskAgg);
            log.info("任务状态同步为进行中，飞书任务ID：{}", feishuTaskId);
        }
    }

    /**
     * 同步飞书任务详情到本地任务
     *
     * @param feishuTaskId 飞书任务ID
     * @param title 任务标题
     * @param description 任务描述
     * @param ownerOpenId 负责人OpenID
     * @param ownerName 负责人名称
     * @param priority 优先级
     * @param dueDate 截止时间
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncTaskDetails(String feishuTaskId, String title, String description,
                              String ownerOpenId, String ownerName, TaskPriorityEnum priority,
                              LocalDateTime dueDate) {
        log.info("同步飞书任务详情，飞书任务ID：{}", feishuTaskId);

        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        if (taskAgg == null) {
            log.warn("未找到对应的本地任务，飞书任务ID：{}", feishuTaskId);
            return;
        }

        // 使用系统用户作为操作人更新任务详情
        taskAgg.update(title, description, ownerOpenId, ownerName, priority, dueDate,
                      taskAgg.getInfo().getMeetingId(), "system", "系统");
        taskDomainService.update(taskAgg);

        log.info("任务详情同步完成，飞书任务ID：{}", feishuTaskId);
    }

    /**
     * 删除飞书任务对应的本地任务
     *
     * @param feishuTaskId 飞书任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByFeishuTaskId(String feishuTaskId) {
        log.info("删除飞书任务对应的本地任务，飞书任务ID：{}", feishuTaskId);

        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        if (taskAgg == null) {
            log.warn("未找到对应的本地任务，飞书任务ID：{}", feishuTaskId);
            return;
        }

        taskDomainService.deleteByFeishuTaskId(feishuTaskId);
        log.info("删除本地任务成功，飞书任务ID：{}", feishuTaskId);
    }

    /**
     * 检查飞书任务是否存在对应的本地任务
     *
     * @param feishuTaskId 飞书任务ID
     * @return 是否存在
     */
    public boolean existsByFeishuTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        return taskAgg != null;
    }

    /**
     * 获取飞书任务对应的本地任务ID
     *
     * @param feishuTaskId 飞书任务ID
     * @return 本地任务ID，如果不存在返回null
     */
    public Long getLocalTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        return taskAgg != null ? taskAgg.getInfo().getId() : null;
    }
}

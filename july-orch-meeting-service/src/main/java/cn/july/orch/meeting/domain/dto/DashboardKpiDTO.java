package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.GrowthTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 仪表板KPI数据传输对象
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardKpiDTO {
    
    @ApiModelProperty("总会议数")
    private KpiItemDTO totalMeetings;
    
    @ApiModelProperty("待办任务数")
    private KpiItemDTO pendingTasks;
    
    @ApiModelProperty("平均满意度")
    private KpiItemDTO averageSatisfaction;
    
    @ApiModelProperty("效率提升")
    private KpiItemDTO efficiencyImprovement;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KpiItemDTO {
        @ApiModelProperty("本周期数值")
        private Double currentPeriod;
        
        @ApiModelProperty("上周期数值")
        private Double previousPeriod;
        
        @ApiModelProperty("增长率(%)")
        private Double growthRate;
        
        @ApiModelProperty("增长类型: INCREASE/DECREASE/EQUAL")
        private GrowthTypeEnum growthType;
    }
}

package cn.july.orch.meeting.domain.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 会议开始事件命令
 * @date 2024-12-30
 */
@Data
public class MeetingStartEndEventCommand {

    private Meeting meeting;
    @Data
    public static class Meeting {

        private String id;
        private String topic;
        @JsonProperty("meeting_no")
        private String meetingNo;
        @JsonProperty("meeting_source")
        private Integer meetingSource;
        @JsonProperty("start_time")
        private String startTime;
        @JsonProperty("end_time")
        private String endTime;
        @JsonProperty("calendar_event_id")
        private String calendarEventId;
    }
}

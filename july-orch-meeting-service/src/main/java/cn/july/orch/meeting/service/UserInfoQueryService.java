package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.config.UserInfoDTO;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import com.lark.oapi.service.contact.v3.model.AvatarInfo;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 用户信息查询服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserInfoQueryService {

    private final FeishuAppClient feishuAppClient;

    @Resource
    private SsoActionService ssoActionService;

    /**
     * 根据openId获取用户头像URL
     *
     * @param openId 用户openId
     * @return 头像URL
     */
    public String getUserAvatarUrl(String openId) {
        if (StrUtil.isBlank(openId)) {
            return null;
        }

        try {
            // 先尝试从缓存中获取用户信息
            UserInfoDTO cachedUserInfo = ssoActionService.getUserToken(openId);
            if (cachedUserInfo != null && StrUtil.isNotBlank(cachedUserInfo.getAvatarUrl())) {
                return cachedUserInfo.getAvatarUrl();
            }

            // 如果缓存中没有，则调用飞书API获取
            List<User> users = feishuAppClient.getContactService().userBatch(Collections.singletonList(openId));
            if (CollUtil.isNotEmpty(users)) {
                User user = users.get(0);
                return Optional.ofNullable(user.getAvatar())
                        .map(AvatarInfo::getAvatarOrigin)
                        .orElse(null);
            }

        } catch (Exception e) {
            log.warn("获取用户头像失败，openId: {}", openId, e);
        }

        return null;
    }

    /**
     * 批量获取用户头像URL
     *
     * @param openIds 用户openId列表
     * @return openId -> 头像URL的映射
     */
    public Map<String, String> getUserAvatarUrls(List<String> openIds) {
        if (CollUtil.isEmpty(openIds)) {
            return Collections.emptyMap();
        }

        try {
            // 调用飞书API批量获取用户信息
            List<User> users = feishuAppClient.getContactService().userBatch(openIds);
            if (CollUtil.isNotEmpty(users)) {
                return users.stream()
                        .collect(Collectors.toMap(
                                User::getOpenId,
                                user -> Optional.ofNullable(user.getAvatar())
                                        .map(AvatarInfo::getAvatarOrigin)
                                        .orElse(null),
                                (existing, replacement) -> existing
                        ));
            }

        } catch (Exception e) {
            log.warn("批量获取用户头像失败，openIds: {}", openIds, e);
        }

        return Collections.emptyMap();
    }

    /**
     * 根据openId获取用户基本信息（包含头像）
     *
     * @param openId 用户openId
     * @return 用户基本信息
     */
    public FSUserInfoDTO getUserInfo(String openId) {
        if (openId == null || openId.trim().isEmpty()) {
            return null;
        }

        try {
            // 调用飞书API获取用户信息
            List<User> users = feishuAppClient.getContactService().userBatch(Collections.singletonList(openId));
            if (users != null && !users.isEmpty()) {
                User user = users.get(0);
                return FSUserInfoDTO.builder()
                        .openId(user.getOpenId())
                        .name(user.getName())
                        .avatarUrl(Optional.ofNullable(user.getAvatar())
                                .map(AvatarInfo::getAvatarOrigin)
                                .orElse(null))
                        .build();
            }

        } catch (Exception e) {
            log.warn("获取用户信息失败，openId: {}", openId, e);
        }

        return null;
    }

    /**
     * 批量获取用户基本信息（包含头像）
     *
     * @param openIds 用户openId列表
     * @return openId -> 用户基本信息的映射
     */
    public Map<String, FSUserInfoDTO> getUserInfos(List<String> openIds) {
        if (openIds == null || openIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 过滤有效的openId
            List<String> validOpenIds = openIds.stream()
                    .filter(openId -> openId != null && !openId.trim().isEmpty())
                    .collect(Collectors.toList());

            if (validOpenIds.isEmpty()) {
                return Collections.emptyMap();
            }

            // 调用飞书API批量获取用户信息
            List<User> users = feishuAppClient.getContactService().userBatch(validOpenIds);
            if (users != null && !users.isEmpty()) {
                return users.stream()
                        .collect(Collectors.toMap(
                                User::getOpenId,
                                user -> FSUserInfoDTO.builder()
                                        .openId(user.getOpenId())
                                        .name(user.getName())
                                        .avatarUrl(Optional.ofNullable(user.getAvatar())
                                                .map(AvatarInfo::getAvatarOrigin)
                                                .orElse(null))
                                        .build(),
                                (existing, replacement) -> existing
                        ));
            }

        } catch (Exception e) {
            log.warn("批量获取用户信息失败，openIds: {}", openIds, e);
        }

        return Collections.emptyMap();
    }
}

package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.enums.TaskStatusEnum;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务仓储接口
 */
public interface ITaskRepository {

    /**
     * 插入任务
     *
     * @param taskAgg 任务聚合
     * @return 任务ID
     */
    Long insert(TaskAgg taskAgg);

    /**
     * 更新任务
     *
     * @param taskAgg 任务聚合
     */
    void update(TaskAgg taskAgg);

    /**
     * 根据ID查询任务
     *
     * @param id 任务ID
     * @return 任务聚合
     */
    TaskAgg findById(Long id);

    /**
     * 根据会议ID查询任务列表
     *
     * @param meetingId 会议ID
     * @return 任务聚合列表
     */
    List<TaskAgg> findByMeetingId(Long meetingId);

    /**
     * 根据状态查询任务列表
     *
     * @param status 任务状态
     * @return 任务聚合列表
     */
    List<TaskAgg> findByStatus(TaskStatusEnum status);

    /**
     * 根据负责人查询任务列表
     *
     * @param ownerOpenId 负责人OpenID
     * @return 任务聚合列表
     */
    List<TaskAgg> findByOwner(String ownerOpenId);

    /**
     * 删除任务
     *
     * @param taskAgg 任务聚合
     */
    void delete(TaskAgg taskAgg);

    /**
     * 查询所有超期任务
     *
     * @return 超期任务列表
     */
    List<TaskAgg> findOverdueTasks();

    /**
     * 根据飞书任务ID查询任务
     *
     * @param feishuTaskId 飞书任务ID
     * @return 任务聚合
     */
    TaskAgg findByFeishuTaskId(String feishuTaskId);
}

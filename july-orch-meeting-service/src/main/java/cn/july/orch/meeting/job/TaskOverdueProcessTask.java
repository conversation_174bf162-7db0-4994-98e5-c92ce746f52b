package cn.july.orch.meeting.job;

import cn.july.orch.meeting.service.TaskActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 任务超期处理定时任务
 */
@Slf4j
@Component
public class TaskOverdueProcessTask {

    @Resource
    private TaskActionService taskActionService;

    /**
     * 处理超期任务
     * 每小时执行一次，检查并标记超期任务
     */
    @Scheduled(fixedRate = 60L * 60 * 1000) // 每小时执行一次
    public void processOverdueTasks() {
        try {
            log.info("开始处理超期任务");
            taskActionService.processOverdueTasks();
            log.info("处理超期任务完成");
        } catch (Exception e) {
            log.error("处理超期任务失败", e);
        }
    }
}

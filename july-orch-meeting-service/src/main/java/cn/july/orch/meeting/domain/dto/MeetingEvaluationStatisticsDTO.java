package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 会议评价统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("会议评价统计DTO")
public class MeetingEvaluationStatisticsDTO {

    @ApiModelProperty("评价总条数")
    private Integer totalCount;

    @ApiModelProperty("会议评分平均分")
    private Double averageMeetingScore;

    @ApiModelProperty("内容评分平均分")
    private Double averageContentScore;

    @ApiModelProperty("时长评分平均分")
    private Double averageDurationScore;

    @ApiModelProperty("效果评分平均分")
    private Double averageEffectivenessScore;

    @ApiModelProperty("各项评分数组 [会议评分, 内容评分, 时长评分, 效果评分]")
    private List<Double> scoreArray;

    @ApiModelProperty("改进建议列表")
    private List<String> suggestionsList;
} 
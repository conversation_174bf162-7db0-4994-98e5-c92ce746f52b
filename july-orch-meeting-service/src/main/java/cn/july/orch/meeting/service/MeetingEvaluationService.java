package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.command.MeetingEvaluationSubmitCommand;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import cn.july.orch.meeting.domain.dto.MeetingEvaluationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议评价服务
 * @date 2025-01-24
 */
@Slf4j
@Service
public class MeetingEvaluationService {
    
    @Resource
    private MeetingEvaluationActionService meetingEvaluationActionService;
    
    @Resource
    private UserInfoQueryService userInfoQueryService;
    
    /**
     * 保存会议评价
     */
    public void saveMeetingEvaluation(Long meetingId, String evaluatorOpenId, MeetingEvaluationDTO evaluationDTO) {
        log.info("保存会议评价，会议ID：{}，评价人：{}", meetingId, evaluatorOpenId);
        
        // 获取评价人姓名
        String evaluatorName = getEvaluatorName(evaluatorOpenId);
        
        // 构建提交命令
        MeetingEvaluationSubmitCommand command = MeetingEvaluationSubmitCommand.builder()
            .meetingId(meetingId)
            .evaluatorOpenId(evaluatorOpenId)
            .evaluatorName(evaluatorName)
            .evaluationDTO(evaluationDTO)
            .build();
        
        // 提交评价
        meetingEvaluationActionService.submitEvaluation(command);
        
        log.info("会议评价保存成功，会议ID：{}，评价人：{}", meetingId, evaluatorOpenId);
    }
    
    /**
     * 获取评价人姓名
     */
    private String getEvaluatorName(String evaluatorOpenId) {
        try {
            FSUserInfoDTO userInfo = userInfoQueryService.getUserInfo(evaluatorOpenId);
            return userInfo != null ? userInfo.getName() : "未知用户";
        } catch (Exception e) {
            log.warn("获取用户信息失败，OpenID：{}", evaluatorOpenId, e);
            return "未知用户";
        }
    }
}

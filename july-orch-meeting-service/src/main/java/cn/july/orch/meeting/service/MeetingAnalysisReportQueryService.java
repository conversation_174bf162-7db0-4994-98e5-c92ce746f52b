package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.*;
import cn.july.orch.meeting.domain.entity.MeetingAnalysisReport;
import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.domain.query.MeetingAnalysisReportQuery;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.repository.IMeetingAnalysisReportRepository;
import cn.july.orch.meeting.repository.ITaskRepository;
import cn.july.orch.meeting.utils.ScoreLevelUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告查询服务
 */
@Slf4j
@Service
public class MeetingAnalysisReportQueryService {

    @Resource
    private IMeetingAnalysisReportRepository meetingAnalysisReportRepository;

    @Resource
    private ITaskRepository taskRepository;

    @Resource
    private NewMeetingQueryService newMeetingQueryService;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 根据ID查询会议分析报告
     */
    public MeetingAnalysisReportDTO queryById(Long id) {
        log.info("根据ID查询会议分析报告，报告ID：{}", id);

        MeetingAnalysisReport report = meetingAnalysisReportRepository.findById(id);
        if (report == null) {
            return null;
        }

        return convertToDTO(report);
    }

    /**
     * 根据会议ID查询会议分析报告
     */
    public MeetingAnalysisReportDTO queryByMeetingId(Long meetingId) {
        log.info("根据会议ID查询会议分析报告，会议ID：{}", meetingId);

        MeetingAnalysisReport report = meetingAnalysisReportRepository.findByMeetingId(meetingId);
        if (report == null) {
            return null;
        }

        MeetingAnalysisReportDTO dto = convertToDTO(report);
        
        // 添加任务统计信息
        dto.setTaskStatistics(getTaskStatistics(meetingId));

        return dto;
    }

    /**
     * 根据查询条件查询会议分析报告
     */
    public MeetingAnalysisReportDTO query(MeetingAnalysisReportQuery query) {
        log.info("查询会议分析报告，查询条件：{}", query);

        // 优先根据报告ID查询
        if (query.getId() != null) {
            return queryById(query.getId());
        }

        // 其次根据会议ID查询
        if (query.getMeetingId() != null) {
            return queryByMeetingId(query.getMeetingId());
        }

        return null;
    }

    /**
     * 获取任务统计信息
     */
    private TaskStatisticsDTO getTaskStatistics(Long meetingId) {
        // 查询会议下的所有任务
        List<TaskAgg> tasks = taskRepository.findByMeetingId(meetingId);
        
        TaskStatisticsDTO statistics = new TaskStatisticsDTO();
        
        // 设置任务总数
        int totalCount = tasks.size();
        statistics.setTotalCount(totalCount);

        // 计算完成率
        long completedCount = tasks.stream()
                .filter(task -> TaskStatusEnum.COMPLETED.equals(task.getInfo().getStatus()))
                .count();
        double completionRate = totalCount > 0 ? (double) completedCount / totalCount * 100 : 0;
        statistics.setCompletionRate(Math.round(completionRate * 100.0) / 100.0); // 保留两位小数

        // 统计各状态任务数量
        Map<String, Integer> statusCounts = new HashMap<>();
        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            int count = (int) tasks.stream()
                    .filter(task -> status.equals(task.getInfo().getStatus()))
                    .count();
            statusCounts.put(status.name(), count);
        }
        statistics.setStatusCounts(statusCounts);

        // 转换任务列表
        List<TaskInfoDTO> taskInfos = tasks.stream()
                .map(task -> {
                    TaskInfoDTO taskInfo = new TaskInfoDTO();
                    taskInfo.setId(task.getInfo().getId());
                    taskInfo.setTitle(task.getInfo().getTitle());
                    taskInfo.setDescription(task.getInfo().getDescription());
                    taskInfo.setDueDate(task.getInfo().getDueDate());
                    taskInfo.setOwnerOpenId(task.getInfo().getOwnerOpenId());
                    taskInfo.setOwnerName(task.getInfo().getOwnerName());
                    taskInfo.setStatus(task.getInfo().getStatus());
                    return taskInfo;
                })
                .collect(Collectors.toList());
        statistics.setTasks(taskInfos);

        return statistics;
    }

    /**
     * 将实体转换为DTO
     */
    private MeetingAnalysisReportDTO convertToDTO(MeetingAnalysisReport report) {
        MeetingAnalysisReportDTO dto = new MeetingAnalysisReportDTO();
        BeanUtils.copyProperties(report, dto);

        // 设置评分等级
        dto.setOverallScoreLevel(ScoreLevelUtils.getScoreLevel(report.getOverallScore()));

        // 设置会议基本信息
        NewMeetingDTO meeting = newMeetingQueryService.getById(report.getMeetingId());
        if (meeting != null) {
            dto.setMeetingName(meeting.getMeetingName());
            dto.setStartTime(meeting.getActualStartTime());
            dto.setEndTime(meeting.getActualEndTime());
            dto.setMeetingLocation(meeting.getMeetingLocation());
            dto.setAttendeeCount(meeting.getActualAttendees().size());
        }

        // 解析内容分析JSON
        if (StringUtils.hasText(report.getContentAnalysisJson())) {
            try {
                dto.setContentAnalysis(objectMapper.readValue(report.getContentAnalysisJson(), ContentAnalysis.class));
            } catch (JsonProcessingException e) {
                log.error("解析内容分析JSON失败", e);
            }
        }

        // 解析AI建议JSON
        if (StringUtils.hasText(report.getAiSuggestionsJson())) {
            try {
                dto.setAiSuggestions(objectMapper.readValue(report.getAiSuggestionsJson(), new TypeReference<List<AiSuggestion>>() {}));
            } catch (JsonProcessingException e) {
                log.error("解析AI建议JSON失败", e);
            }
        }

        return dto;
    }
}


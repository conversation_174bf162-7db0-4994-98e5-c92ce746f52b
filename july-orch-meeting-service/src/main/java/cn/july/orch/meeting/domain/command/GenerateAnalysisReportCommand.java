package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Assistant
 * @description 生成会议分析报告命令
 */
@Data
@ApiModel("生成会议分析报告命令")
public class GenerateAnalysisReportCommand {

    @NotNull(message = "会议ID不能为空")
    @ApiModelProperty("会议ID")
    private Long meetingId;
}
package cn.july.orch.meeting.domain.po;

import cn.july.core.model.enums.DeletedEnum;
import cn.july.database.mybatisplus.typehandler.ListStringTypeHandler;
import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划持久化对象
 * @date 2025-01-24
 */
@Data
@Accessors(chain = true)
@TableName("meeting_plan")
public class MeetingPlanPO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会议规划名称
     */
    @TableField("plan_name")
    private String planName;

    /**
     * 会议规划描述/备注
     */
    @TableField("plan_description")
    private String planDescription;

    /**
     * 计划开始时间
     */
    @TableField("planned_start_time")
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @TableField("planned_end_time")
    private LocalDateTime plannedEndTime;

    /**
     * 计划持续时长(分钟)
     */
    @TableField("planned_duration")
    private Integer plannedDuration;

    /**
     * 会议规划状态
     */
    @TableField("status")
    private MeetingPlanStatusEnum status;

    /**
     * 会议标准ID
     */
    @TableField("meeting_standard_id")
    private Long meetingStandardId;

    /**
     * 优先级
     */
    @TableField("priority_level")
    private PriorityLevelEnum priorityLevel;

    /**
     * 部门ID
     */
    @TableField("department_id")
    private Long departmentId;

    /**
     * 部门名称
     */
    @TableField("department_name")
    private String departmentName;

    /**
     * 业务会议ID
     */
    @TableField("business_meeting_id")
    private Long businessMeetingId;

    /**
     * 参会人员列表
     */
    @TableField(value = "attendees", typeHandler = ListStringTypeHandler.class)
    private List<String> attendees;

    /**
     * 会议地点
     */
    @TableField("meeting_location")
    private String meetingLocation;

    /**
     * 提前通知发送标记(0-未发送,1-已发送)
     */
    @TableField("advance_notice_sent")
    private Integer advanceNoticeSent;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记(0-未删除,1-已删除)
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 是否重复会议
     */
    @TableField("is_recurring")
    private IsRecurringEnum isRecurring;

    /**
     * 重复类型
     */
    @TableField("recurrence_type")
    private RecurrenceTypeEnum recurrenceType;

    /**
     * 重复间隔
     */
    @TableField("recurrence_interval")
    private Integer recurrenceInterval;

    /**
     * 每周重复的星期几
     */
    @TableField("recurrence_weekdays")
    private String recurrenceWeekdays;

    /**
     * 每月重复的日期
     */
    @TableField("recurrence_month_day")
    private Integer recurrenceMonthDay;

    /**
     * 重复结束日期
     */
    @TableField("recurrence_end_date")
    private LocalDate recurrenceEndDate;

    /**
     * 会前文档文件ID列表
     */
    @TableField(value = "pre_meeting_documents", typeHandler = JacksonTypeHandler.class)
    private List<String> preMeetingDocuments;


}

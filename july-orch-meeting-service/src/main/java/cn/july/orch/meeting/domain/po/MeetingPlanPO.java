package cn.july.orch.meeting.domain.po;

import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.RecurrenceTypeEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划持久化对象
 * @date 2025-01-24
 */
@Data
@TableName("meeting_plan")
public class MeetingPlanPO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String planName;

    private String planDescription;

    private LocalDateTime plannedStartTime;

    private LocalDateTime plannedEndTime;

    private Integer plannedDuration;

    private MeetingPlanStatusEnum status;

    private Long meetingStandardId;

    private PriorityLevelEnum priorityLevel;

    private Long departmentId;

    private String departmentName;

    private Long businessMeetingId;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attendees;

    private String meetingLocation;

    private Integer advanceNoticeSent;

    private String createUserId;

    private String createUserName;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private String updateUserId;

    private String updateUserName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否重复会议
     */
    @TableField("is_recurring")
    private IsRecurringEnum isRecurring;

    /**
     * 重复类型
     */
    @TableField("recurrence_type")
    private RecurrenceTypeEnum recurrenceType;

    /**
     * 重复间隔
     */
    @TableField("recurrence_interval")
    private Integer recurrenceInterval;

    /**
     * 每周重复的星期几
     */
    @TableField("recurrence_weekdays")
    private String recurrenceWeekdays;

    /**
     * 每月重复的日期
     */
    @TableField("recurrence_month_day")
    private Integer recurrenceMonthDay;

    /**
     * 重复结束日期
     */
    @TableField("recurrence_end_date")
    private LocalDate recurrenceEndDate;

    /**
     * 会前文档文件ID列表
     */
    @TableField(value = "pre_meeting_documents", typeHandler = JacksonTypeHandler.class)
    private List<String> preMeetingDocuments;
}

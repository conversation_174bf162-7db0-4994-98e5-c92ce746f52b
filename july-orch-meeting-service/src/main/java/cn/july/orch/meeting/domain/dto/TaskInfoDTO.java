package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.TaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务信息DTO
 */
@Data
@ApiModel("任务信息DTO")
public class TaskInfoDTO {
    
    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("任务标题")
    private String title;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty("截止时间")
    private LocalDateTime dueDate;

    @ApiModelProperty("负责人ID")
    private String ownerOpenId;

    @ApiModelProperty("负责人名称")
    private String ownerName;

    @ApiModelProperty("任务状态（0:未开始；1:进行中；2:已完成；3:已超期）")
    private TaskStatusEnum status;
}
package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.model.enums.DeletedEnum;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.dto.DashboardKpiDTO;
import cn.july.orch.meeting.domain.dto.DashboardTrendDTO;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import cn.july.orch.meeting.domain.dto.RecentMeetingDTO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.domain.po.TaskPO;
import cn.july.orch.meeting.domain.query.DashboardKpiQuery;
import cn.july.orch.meeting.domain.query.DashboardTrendQuery;
import cn.july.orch.meeting.domain.query.RecentMeetingQuery;
import cn.july.orch.meeting.enums.GrowthTypeEnum;
import cn.july.orch.meeting.enums.MeetingStatusFilterEnum;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import cn.july.orch.meeting.mapper.TaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 仪表板查询服务实现类
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardQueryService {

    private final NewMeetingMapper newMeetingMapper;
    private final TaskMapper taskMapper;
    private final UserInfoQueryService userInfoQueryService;

    public DashboardKpiDTO getKpi(DashboardKpiQuery query) {
        try {
            // 计算当前周期时间范围
            LocalDateTime currentStart = query.getStartDate().atStartOfDay();
            LocalDateTime currentEnd = query.getEndDate().atTime(23, 59, 59);

            // 根据时间单位计算上一周期时间范围
            LocalDateTime[] previousPeriod = calculatePreviousPeriod(query.getStartDate(), query.getEndDate(), query.getTimeUnit());
            LocalDateTime previousStart = previousPeriod[0];
            LocalDateTime previousEnd = previousPeriod[1];

            // 查询总会议数
            DashboardKpiDTO.KpiItemDTO totalMeetings = getMeetingCount(currentStart, currentEnd, previousStart, previousEnd);

            // 查询待办任务数
            DashboardKpiDTO.KpiItemDTO pendingTasks = getPendingTaskCount(currentStart, currentEnd, previousStart, previousEnd);

            // 固定数据：平均满意度
            DashboardKpiDTO.KpiItemDTO averageSatisfaction = DashboardKpiDTO.KpiItemDTO.builder()
                    .currentPeriod(4.8)
                    .previousPeriod(4.6)
                    .growthRate(4.3)
                    .growthType(GrowthTypeEnum.INCREASE)
                    .build();

            // 固定数据：效率提升
            DashboardKpiDTO.KpiItemDTO efficiencyImprovement = DashboardKpiDTO.KpiItemDTO.builder()
                    .currentPeriod(85.2)
                    .previousPeriod(82.1)
                    .growthRate(3.8)
                    .growthType(GrowthTypeEnum.INCREASE)
                    .build();

            return DashboardKpiDTO.builder()
                    .totalMeetings(totalMeetings)
                    .pendingTasks(pendingTasks)
                    .averageSatisfaction(averageSatisfaction)
                    .efficiencyImprovement(efficiencyImprovement)
                    .build();
        } catch (Exception e) {
            log.error("获取KPI数据失败", e);
            throw new BusinessException("获取KPI数据失败");
        }
    }

    public DashboardTrendDTO getTrend(DashboardTrendQuery query) {
        try {
            List<String> timeLabels = new ArrayList<>();
            List<Integer> completedMeetings = new ArrayList<>();
            List<Integer> plannedMeetings = new ArrayList<>();
            List<Integer> temporaryMeetings = new ArrayList<>();

            // 根据时间单位生成时间标签和数据
            switch (query.getTimeUnit()) {
                case WEEK:
                    // 周：显示这一周内每天的数据
                    generateDailyData(query.getStartDate(), query.getEndDate(), timeLabels, completedMeetings, plannedMeetings, temporaryMeetings);
                    break;
                case MONTH:
                    // 月：显示这一个月内每周的数据
                    generateWeeklyData(query.getStartDate(), query.getEndDate(), timeLabels, completedMeetings, plannedMeetings, temporaryMeetings);
                    break;
                case YEAR:
                    // 年：显示这一年每月的数据
                    generateMonthlyData(query.getStartDate(), query.getEndDate(), timeLabels, completedMeetings, plannedMeetings, temporaryMeetings);
                    break;
                default:
                    // 默认按天处理
                    generateDailyData(query.getStartDate(), query.getEndDate(), timeLabels, completedMeetings, plannedMeetings, temporaryMeetings);
            }

            List<DashboardTrendDTO.TrendSeriesDTO> series = new ArrayList<>();
            series.add(DashboardTrendDTO.TrendSeriesDTO.builder()
                    .name("已完成会议")
                    .data(completedMeetings)
                    .build());
            series.add(DashboardTrendDTO.TrendSeriesDTO.builder()
                    .name("计划会议")
                    .data(plannedMeetings)
                    .build());
            series.add(DashboardTrendDTO.TrendSeriesDTO.builder()
                    .name("临时会议")
                    .data(temporaryMeetings)
                    .build());

            return DashboardTrendDTO.builder()
                    .timeLabels(timeLabels)
                    .series(series)
                    .build();
        } catch (Exception e) {
            log.error("获取趋势分析数据失败", e);
            throw new BusinessException("获取趋势分析数据失败");
        }
    }

    public PageResultDTO<RecentMeetingDTO> getRecentMeetings(RecentMeetingQuery query) {
        try {
            Page<NewMeetingPO> page = new Page<>(query.getPageNo(), query.getPageSize());

            LambdaQueryWrapper<NewMeetingPO> wrapper = new LambdaQueryWrapper<>();

            // 逻辑删除条件：只查询未删除的数据
            wrapper.eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);

            // 状态筛选
            if (MeetingStatusFilterEnum.ONGOING.equals(query.getStatus())) {
                wrapper.eq(NewMeetingPO::getStatus, NewMeetingStatusEnum.IN_PROCESS);
            } else if (MeetingStatusFilterEnum.COMPLETED.equals(query.getStatus())) {
                wrapper.eq(NewMeetingPO::getStatus, NewMeetingStatusEnum.ENDED);
            }

            // 按开始时间倒序排列，显示最近的会议
            wrapper.orderByDesc(NewMeetingPO::getStartTime);

            IPage<NewMeetingPO> result = newMeetingMapper.selectPage(page, wrapper);

            List<RecentMeetingDTO> records = result.getRecords().stream()
                    .map(this::convertToRecentMeetingDTO)
                    .collect(Collectors.toList());

            return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), result.getTotal(), records);
        } catch (Exception e) {
            log.error("获取近期会议列表失败", e);
            throw new BusinessException("获取近期会议列表失败");
        }
    }

    /**
     * 根据时间单位计算上一周期的时间范围
     */
    private LocalDateTime[] calculatePreviousPeriod(LocalDate startDate, LocalDate endDate, TimeUnitEnum timeUnit) {
        LocalDateTime currentStart = startDate.atStartOfDay();
        LocalDateTime currentEnd = endDate.atTime(23, 59, 59);

        LocalDateTime previousStart;
        LocalDateTime previousEnd;

        switch (timeUnit) {
            case DAY:
                // 按天：上一天
                long days = java.time.Duration.between(currentStart, currentEnd).toDays() + 1;
                previousStart = currentStart.minusDays(days);
                previousEnd = currentStart.minusSeconds(1);
                break;
            case WEEK:
                // 按周：上一周
                long weeks = java.time.temporal.ChronoUnit.WEEKS.between(startDate, endDate) + 1;
                previousStart = currentStart.minusWeeks(weeks);
                previousEnd = currentStart.minusSeconds(1);
                break;
            case MONTH:
                // 按月：上一月
                long months = java.time.temporal.ChronoUnit.MONTHS.between(startDate, endDate) + 1;
                previousStart = currentStart.minusMonths(months);
                previousEnd = currentStart.minusSeconds(1);
                break;
            default:
                // 默认按天处理
                long defaultDays = java.time.Duration.between(currentStart, currentEnd).toDays() + 1;
                previousStart = currentStart.minusDays(defaultDays);
                previousEnd = currentStart.minusSeconds(1);
        }

        return new LocalDateTime[]{previousStart, previousEnd};
    }

    private DashboardKpiDTO.KpiItemDTO getMeetingCount(LocalDateTime currentStart, LocalDateTime currentEnd,
                                                      LocalDateTime previousStart, LocalDateTime previousEnd) {
        try {
            // 查询本周期会议数 - 使用startTime字段
            LambdaQueryWrapper<NewMeetingPO> currentWrapper = new LambdaQueryWrapper<>();
            currentWrapper.between(NewMeetingPO::getStartTime, currentStart, currentEnd)
                    .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);
            long currentCount = newMeetingMapper.selectCount(currentWrapper);

            // 查询上周期会议数 - 使用startTime字段
            LambdaQueryWrapper<NewMeetingPO> previousWrapper = new LambdaQueryWrapper<>();
            previousWrapper.between(NewMeetingPO::getStartTime, previousStart, previousEnd)
                    .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);
            long previousCount = newMeetingMapper.selectCount(previousWrapper);

            return calculateKpiItem(currentCount, previousCount);
        } catch (Exception e) {
            log.error("查询会议数量失败", e);
            return DashboardKpiDTO.KpiItemDTO.builder()
                    .currentPeriod(0.0)
                    .previousPeriod(0.0)
                    .growthRate(0.0)
                    .growthType(GrowthTypeEnum.EQUAL)
                    .build();
        }
    }

    private DashboardKpiDTO.KpiItemDTO getPendingTaskCount(LocalDateTime currentStart, LocalDateTime currentEnd,
                                                          LocalDateTime previousStart, LocalDateTime previousEnd) {
        try {
            // 查询本周期待办任务数 - 使用createTime字段（任务创建时间）
            LambdaQueryWrapper<TaskPO> currentWrapper = new LambdaQueryWrapper<>();
            currentWrapper.between(TaskPO::getCreateTime, currentStart, currentEnd)
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            long currentCount = taskMapper.selectCount(currentWrapper);

            // 查询上周期待办任务数
            LambdaQueryWrapper<TaskPO> previousWrapper = new LambdaQueryWrapper<>();
            previousWrapper.between(TaskPO::getCreateTime, previousStart, previousEnd)
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            long previousCount = taskMapper.selectCount(previousWrapper);

            return calculateKpiItem(currentCount, previousCount);
        } catch (Exception e) {
            log.error("查询待办任务数量失败", e);
            return DashboardKpiDTO.KpiItemDTO.builder()
                    .currentPeriod(0.0)
                    .previousPeriod(0.0)
                    .growthRate(0.0)
                    .growthType(GrowthTypeEnum.EQUAL)
                    .build();
        }
    }

    private DashboardKpiDTO.KpiItemDTO calculateKpiItem(long current, long previous) {
        double growthRate = 0.0;
        GrowthTypeEnum growthType = GrowthTypeEnum.EQUAL;

        if (previous > 0) {
            growthRate = ((double) (current - previous) / previous) * 100;
        } else if (current > 0) {
            growthRate = 100.0;
        }

        if (current > previous) {
            growthType = GrowthTypeEnum.INCREASE;
        } else if (current < previous) {
            growthType = GrowthTypeEnum.DECREASE;
        }

        return DashboardKpiDTO.KpiItemDTO.builder()
                .currentPeriod((double) current)
                .previousPeriod((double) previous)
                .growthRate(Math.round(growthRate * 10.0) / 10.0) // 保留一位小数
                .growthType(growthType)
                .build();
    }

    private int countCompletedMeetings(LocalDateTime start, LocalDateTime end) {
        try {
            LambdaQueryWrapper<NewMeetingPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.between(NewMeetingPO::getStartTime, start, end)
                    .eq(NewMeetingPO::getStatus, NewMeetingStatusEnum.ENDED)
                    .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);
            return Math.toIntExact(newMeetingMapper.selectCount(wrapper));
        } catch (Exception e) {
            log.error("查询已完成会议数量失败", e);
            return 0;
        }
    }

    private int countPlannedMeetings(LocalDateTime start, LocalDateTime end) {
        try {
            // 计划会议：有meetingPlanId的会议
            LambdaQueryWrapper<NewMeetingPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.between(NewMeetingPO::getStartTime, start, end)
                    .isNotNull(NewMeetingPO::getMeetingPlanId)
                    .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);
            return Math.toIntExact(newMeetingMapper.selectCount(wrapper));
        } catch (Exception e) {
            log.error("查询计划会议数量失败", e);
            return 0;
        }
    }

    private int countTemporaryMeetings(LocalDateTime start, LocalDateTime end) {
        try {
            // 临时会议：没有meetingPlanId的会议
            LambdaQueryWrapper<NewMeetingPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.between(NewMeetingPO::getStartTime, start, end)
                    .isNull(NewMeetingPO::getMeetingPlanId)
                    .eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);
            return Math.toIntExact(newMeetingMapper.selectCount(wrapper));
        } catch (Exception e) {
            log.error("查询临时会议数量失败", e);
            return 0;
        }
    }



    /**
     * 生成每日数据（用于周维度）
     */
    private void generateDailyData(LocalDate startDate, LocalDate endDate, List<String> timeLabels,
                                  List<Integer> completedMeetings, List<Integer> plannedMeetings, List<Integer> temporaryMeetings) {
        try {
            LocalDate current = startDate;
            while (!current.isAfter(endDate)) {
                // 生成时间标签：周一、周二、周三...
                timeLabels.add(formatDayLabel(current));

                // 查询当天的数据
                LocalDateTime start = current.atStartOfDay();
                LocalDateTime end = current.atTime(23, 59, 59);

                completedMeetings.add(countCompletedMeetings(start, end));
                plannedMeetings.add(countPlannedMeetings(start, end));
                temporaryMeetings.add(countTemporaryMeetings(start, end));

                current = current.plusDays(1);
            }
        } catch (Exception e) {
            log.error("生成每日数据失败", e);
            // 添加默认数据避免前端报错
            if (timeLabels.isEmpty()) {
                timeLabels.add("无数据");
                completedMeetings.add(0);
                plannedMeetings.add(0);
                temporaryMeetings.add(0);
            }
        }
    }

    /**
     * 生成每周数据（用于月维度）
     */
    private void generateWeeklyData(LocalDate startDate, LocalDate endDate, List<String> timeLabels,
                                   List<Integer> completedMeetings, List<Integer> plannedMeetings, List<Integer> temporaryMeetings) {
        try {
            // 简化逻辑：按周遍历指定的日期范围
            LocalDate current = startDate.with(java.time.DayOfWeek.MONDAY); // 找到这周的周一
            int weekCount = 1;

            while (!current.isAfter(endDate)) {
                LocalDate weekEnd = current.plusDays(6); // 这周的周日

                // 确保不超过结束日期
                if (weekEnd.isAfter(endDate)) {
                    weekEnd = endDate;
                }

                // 确保不早于开始日期
                LocalDate actualStart = current.isBefore(startDate) ? startDate : current;

                // 生成时间标签：第1周、第2周...
                timeLabels.add("第" + weekCount + "周");

                // 查询这一周的数据
                LocalDateTime start = actualStart.atStartOfDay();
                LocalDateTime end = weekEnd.atTime(23, 59, 59);

                completedMeetings.add(countCompletedMeetings(start, end));
                plannedMeetings.add(countPlannedMeetings(start, end));
                temporaryMeetings.add(countTemporaryMeetings(start, end));

                current = current.plusWeeks(1);
                weekCount++;
            }
        } catch (Exception e) {
            log.error("生成每周数据失败", e);
            // 添加默认数据避免前端报错
            if (timeLabels.isEmpty()) {
                timeLabels.add("无数据");
                completedMeetings.add(0);
                plannedMeetings.add(0);
                temporaryMeetings.add(0);
            }
        }
    }

    /**
     * 生成每月数据（用于年维度）
     */
    private void generateMonthlyData(LocalDate startDate, LocalDate endDate, List<String> timeLabels,
                                    List<Integer> completedMeetings, List<Integer> plannedMeetings, List<Integer> temporaryMeetings) {
        try {
            // 简化逻辑：按月遍历指定的日期范围
            LocalDate current = startDate.withDayOfMonth(1); // 这个月的第一天

            while (!current.isAfter(endDate)) {
                LocalDate monthEnd = current.withDayOfMonth(current.lengthOfMonth()); // 这个月的最后一天

                // 确保不超过结束日期
                if (monthEnd.isAfter(endDate)) {
                    monthEnd = endDate;
                }

                // 确保不早于开始日期
                LocalDate actualStart = current.isBefore(startDate) ? startDate : current;

                // 生成时间标签：1月、2月...
                timeLabels.add(current.getMonthValue() + "月");

                // 查询这个月的数据
                LocalDateTime start = actualStart.atStartOfDay();
                LocalDateTime end = monthEnd.atTime(23, 59, 59);

                completedMeetings.add(countCompletedMeetings(start, end));
                plannedMeetings.add(countPlannedMeetings(start, end));
                temporaryMeetings.add(countTemporaryMeetings(start, end));

                current = current.plusMonths(1);
            }
        } catch (Exception e) {
            log.error("生成每月数据失败", e);
            // 添加默认数据避免前端报错
            if (timeLabels.isEmpty()) {
                timeLabels.add("无数据");
                completedMeetings.add(0);
                plannedMeetings.add(0);
                temporaryMeetings.add(0);
            }
        }
    }

    /**
     * 格式化日期标签（周一、周二等）
     */
    private String formatDayLabel(LocalDate date) {
        java.time.DayOfWeek dayOfWeek = date.getDayOfWeek();
        switch (dayOfWeek) {
            case MONDAY:
                return "周一";
            case TUESDAY:
                return "周二";
            case WEDNESDAY:
                return "周三";
            case THURSDAY:
                return "周四";
            case FRIDAY:
                return "周五";
            case SATURDAY:
                return "周六";
            case SUNDAY:
                return "周日";
            default:
                return date.format(DateTimeFormatter.ofPattern("MM-dd"));
        }
    }

    private RecentMeetingDTO convertToRecentMeetingDTO(NewMeetingPO meeting) {
        try {
            // 查询关联的任务数量
            int taskCount = getTaskCountByMeetingId(meeting.getId());

            // 获取参会人员详细信息
            List<FSUserInfoDTO> attendeeDetails = null;
            if (meeting.getAttendees() != null && !meeting.getAttendees().isEmpty()) {
                try {
                    Map<String, FSUserInfoDTO> userInfoMap = userInfoQueryService.getUserInfos(meeting.getAttendees());
                    attendeeDetails = meeting.getAttendees().stream()
                            .map(userInfoMap::get)
                            .filter(userInfo -> userInfo != null)
                            .collect(Collectors.toList());
                } catch (Exception e) {
                    log.warn("获取参会人员详细信息失败，会议ID: {}", meeting.getId(), e);
                    // 获取失败时，attendeeDetails保持为null
                }
            }

            return RecentMeetingDTO.builder()
                    .id(meeting.getId())
                    .meetingName(meeting.getMeetingName())
                    .meetingDescription(meeting.getMeetingDescription())
                    .meetingPlanId(meeting.getMeetingPlanId())
                    .meetingStandardId(meeting.getMeetingStandardId())
                    .meetingNo(meeting.getMeetingNo())
                    .startTime(meeting.getStartTime())
                    .endTime(meeting.getEndTime())
                    .status(meeting.getStatus())
                    .priorityLevel(meeting.getPriorityLevel())
                    .meetingLocation(meeting.getMeetingLocation())
                    .attendees(meeting.getAttendees())
                    .attendeeDetails(attendeeDetails)
                    .attendeeCount(meeting.getAttendees() != null ? meeting.getAttendees().size() : 0)
                    .hostUserId(meeting.getHostUserId())
                    .recorderUserId(meeting.getRecorderUserId())
                    .fsCalendarEventId(meeting.getFsCalendarEventId())
                    .fsMeetingId(meeting.getFsMeetingId())
                    .meetingUrl(meeting.getMeetingUrl())
                    .minuteUrl(meeting.getMinuteUrl())
                    .hasMinutes(meeting.getMinuteUrl() != null && !meeting.getMinuteUrl().trim().isEmpty())
                    .taskCount(taskCount)
                    .hasAgenda(meeting.getMeetingPlanId() != null) // 有会议规划ID说明有议程
                    .hasMaterials(false) // 暂时固定为false，后续可根据实际业务扩展
                    .createUserId(meeting.getCreateUserId())
                    .createUserName(meeting.getCreateUserName())
                    .createTime(meeting.getCreateTime())
                    .build();
        } catch (Exception e) {
            log.error("转换近期会议DTO失败，会议ID: {}", meeting.getId(), e);
            // 返回基础信息，避免整个接口失败
            return RecentMeetingDTO.builder()
                    .id(meeting.getId())
                    .meetingName(meeting.getMeetingName())
                    .startTime(meeting.getStartTime())
                    .endTime(meeting.getEndTime())
                    .attendeeCount(meeting.getAttendees() != null ? meeting.getAttendees().size() : 0)
                    .status(meeting.getStatus())
                    .hasMinutes(false)
                    .taskCount(0)
                    .hasAgenda(false)
                    .hasMaterials(false)
                    .build();
        }
    }

    /**
     * 根据会议ID查询任务数量
     */
    private int getTaskCountByMeetingId(Long meetingId) {
        try {
            LambdaQueryWrapper<TaskPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskPO::getMeetingId, meetingId)
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);
            return Math.toIntExact(taskMapper.selectCount(wrapper));
        } catch (Exception e) {
            log.error("查询会议任务数量失败，会议ID: {}", meetingId, e);
            return 0;
        }
    }
}

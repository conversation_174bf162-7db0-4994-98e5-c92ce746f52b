package cn.july.orch.meeting.utils;

public class JsonUnescapeUtil {

    public static String unescape(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // Step 1: 去掉外层引号（如果是一个被引号包围的字符串）
        String result = input;
        if (result.startsWith("\"") && result.endsWith("\"")) {
            result = result.substring(1, result.length() - 1);
        }

        // Step 2: 逐个还原转义字符
        result = result
                .replace("\\\"", "\"")   // \" → "
                .replace("\\\\", "\\")   // \\ → \ （注意顺序：先处理 \\，再处理其他）
                .replace("\\/", "/")     // \/ → /
                .replace("\\b", "\b")
                .replace("\\f", "\f")
                .replace("\\n", "\n")
                .replace("\\r", "\r")
                .replace("\\t", "\t");

        // 可选：处理 Unicode 转义（如 \u2022），复杂场景可扩展
        // 这里省略，一般情况不需要

        return result;
    }
}

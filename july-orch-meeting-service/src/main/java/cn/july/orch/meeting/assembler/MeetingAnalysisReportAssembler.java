package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.entity.MeetingAnalysisReport;
import cn.july.orch.meeting.domain.po.MeetingAnalysisReportPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingAnalysisReportAssembler {

    /**
     * 实体转PO
     */
    MeetingAnalysisReportPO toPO(MeetingAnalysisReport meetingAnalysisReport);

    /**
     * PO转实体
     */
    MeetingAnalysisReport toEntity(MeetingAnalysisReportPO meetingAnalysisReportPO);
}
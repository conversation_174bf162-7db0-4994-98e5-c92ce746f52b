package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.model.ddd.IdQuery;
import cn.july.core.model.enums.DeletedEnum;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.assembler.TaskAssembler;
import cn.july.orch.meeting.assembler.TaskConverter;
import cn.july.orch.meeting.domain.dto.TaskDTO;
import cn.july.orch.meeting.domain.dto.TaskListDTO;
import cn.july.orch.meeting.domain.dto.TaskStatusStatisticsDTO;
import cn.july.orch.meeting.domain.dto.TaskSupervisionDTO;
import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.domain.po.TaskPO;
import cn.july.orch.meeting.domain.query.TaskQuery;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.mapper.TaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务查询服务
 */
@Slf4j
@Service
public class TaskQueryService {

    @Resource
    private TaskDomainService taskDomainService;
    @Resource
    private TaskAssembler taskAssembler;
    @Resource
    private TaskMapper taskMapper;
    @Resource
    private TaskConverter taskConverter;

    /**
     * 分页查询任务列表
     *
     * @param taskQuery 查询条件
     * @return 分页结果
     */
    public PageResultDTO<TaskListDTO> page(TaskQuery taskQuery) {
        Page<TaskPO> page = new Page<>(taskQuery.getPageNo(), taskQuery.getPageSize());

        LambdaQueryWrapper<TaskPO> queryWrapper = new LambdaQueryWrapper<>();

        // 逻辑删除条件
        queryWrapper.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);

        // 标题模糊查询
        if (StringUtils.hasText(taskQuery.getTitle())) {
            queryWrapper.like(TaskPO::getTitle, taskQuery.getTitle());
        }

        // 负责人查询
        if (StringUtils.hasText(taskQuery.getOwnerOpenId())) {
            queryWrapper.eq(TaskPO::getOwnerOpenId, taskQuery.getOwnerOpenId());
        }

        // 负责人名称模糊查询
        if (StringUtils.hasText(taskQuery.getOwnerName())) {
            queryWrapper.like(TaskPO::getOwnerName, taskQuery.getOwnerName());
        }

        // 优先级查询
        if (taskQuery.getPriority() != null) {
            queryWrapper.eq(TaskPO::getPriority, taskQuery.getPriority());
        }

        // 状态查询
        if (taskQuery.getStatus() != null) {
            queryWrapper.eq(TaskPO::getStatus, taskQuery.getStatus());
        }

        // 会议ID查询
        if (taskQuery.getMeetingId() != null) {
            queryWrapper.eq(TaskPO::getMeetingId, taskQuery.getMeetingId());
        }

        // 截止时间范围查询
        if (taskQuery.getDueDateStart() != null) {
            queryWrapper.ge(TaskPO::getDueDate, taskQuery.getDueDateStart());
        }
        if (taskQuery.getDueDateEnd() != null) {
            queryWrapper.le(TaskPO::getDueDate, taskQuery.getDueDateEnd());
        }

        // 创建时间范围查询
        if (taskQuery.getCreateTimeStart() != null) {
            queryWrapper.ge(TaskPO::getCreateTime, taskQuery.getCreateTimeStart());
        }
        if (taskQuery.getCreateTimeEnd() != null) {
            queryWrapper.le(TaskPO::getCreateTime, taskQuery.getCreateTimeEnd());
        }

        // 只查询超期任务
        if (Boolean.TRUE.equals(taskQuery.getOnlyOverdue())) {
            queryWrapper.lt(TaskPO::getDueDate, LocalDateTime.now())
                .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED);
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc(TaskPO::getCreateTime);

        IPage<TaskPO> pageResult = taskMapper.selectPage(page, queryWrapper);

        List<TaskListDTO> taskListDTOList = taskConverter.toTaskListDTOList(
            taskAssembler.toTaskInfoList(pageResult.getRecords()));

        return new PageResultDTO<>(taskQuery.getPageNo(), taskQuery.getPageSize(), pageResult.getSize(), taskListDTOList);
    }

    /**
     * 根据ID查询任务详情
     *
     * @param idQuery ID查询
     * @return 任务详情
     */
    public TaskDTO detail(IdQuery idQuery) {
        TaskAgg taskAgg = taskDomainService.findById(idQuery.getId());
        return taskConverter.toTaskDTO(taskAgg);
    }

    /**
     * 根据会议ID查询任务列表
     *
     * @param meetingId 会议ID
     * @return 任务列表
     */
    public List<TaskListDTO> listByMeetingId(Long meetingId) {
        List<TaskAgg> taskAggList = taskDomainService.findByMeetingId(meetingId);
        return taskConverter.toTaskListDTOListFromAgg(taskAggList);
    }

    /**
     * 根据负责人查询任务列表
     *
     * @param ownerOpenId 负责人OpenID
     * @return 任务列表
     */
    public List<TaskListDTO> listByOwner(String ownerOpenId) {
        List<TaskAgg> taskAggList = taskDomainService.findByOwner(ownerOpenId);
        return taskConverter.toTaskListDTOListFromAgg(taskAggList);
    }

    /**
     * 查询我的任务列表
     *
     * @param taskQuery 查询条件
     * @return 分页结果
     */
    public PageResultDTO<TaskListDTO> myTasks(TaskQuery taskQuery) {
        // 这里需要从当前用户上下文获取用户OpenID
        // taskQuery.setOwnerOpenId(getCurrentUserOpenId());
        return page(taskQuery);
    }

    /**
     * 获取任务统计信息
     *
     * @return 任务统计DTO
     */
    public TaskStatusStatisticsDTO getTaskStatistics() {
        log.info("开始获取任务统计信息");

        try {
            // 获取总任务数（排除逻辑删除）
            Long totalTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>().eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));

            // 获取各状态任务数（排除逻辑删除）
            Long notStartedTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .eq(TaskPO::getStatus, TaskStatusEnum.NOT_STARTED.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));
            Long inProgressTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .eq(TaskPO::getStatus, TaskStatusEnum.IN_PROGRESS.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));
            Long completedTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .eq(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));

            // 获取超期任务数（截止时间小于当前时间且未完成，排除逻辑删除）
            Long overdueTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .lt(TaskPO::getDueDate, LocalDateTime.now())
                    .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));

            // 获取今日到期任务数（排除逻辑删除）
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime todayEnd = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            Long todayDueTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .between(TaskPO::getDueDate, todayStart, todayEnd)
                    .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));

            // 获取本周到期任务数（排除逻辑删除）
            LocalDateTime weekStart = LocalDateTime.now().with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime weekEnd = LocalDateTime.now().with(DayOfWeek.SUNDAY).withHour(23).withMinute(59).withSecond(59);
            Long weekDueTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .between(TaskPO::getDueDate, weekStart, weekEnd)
                    .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));

            // 获取高优先级任务数（排除逻辑删除）
            Long highPriorityTasks = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .eq(TaskPO::getPriority, TaskPriorityEnum.HIGH.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));

            // 计算完成率和超期率
            Double completionRate = totalTasks > 0 ? (completedTasks.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;
            Double overdueRate = totalTasks > 0 ? (overdueTasks.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;

            // 获取各状态统计详情
            List<TaskStatusStatisticsDTO.TaskStatusStatistics> statusStatistics = buildStatusStatistics(totalTasks);

            // 获取各优先级统计详情
            List<TaskStatusStatisticsDTO.TaskPriorityStatistics> priorityStatistics = buildPriorityStatistics(totalTasks);

            TaskStatusStatisticsDTO statistics = TaskStatusStatisticsDTO.builder()
                .totalTasks(totalTasks)
                .notStartedTasks(notStartedTasks)
                .inProgressTasks(inProgressTasks)
                .completedTasks(completedTasks)
                .overdueTasks(overdueTasks)
                .todayDueTasks(todayDueTasks)
                .weekDueTasks(weekDueTasks)
                .highPriorityTasks(highPriorityTasks)
                .completionRate(Math.round(completionRate * 100.0) / 100.0)
                .overdueRate(Math.round(overdueRate * 100.0) / 100.0)
                .statusStatistics(statusStatistics)
                .priorityStatistics(priorityStatistics)
                .build();

            log.info("任务统计信息获取成功：总任务数={}, 完成率={}%, 超期率={}%",
                totalTasks, statistics.getCompletionRate(), statistics.getOverdueRate());

            return statistics;

        } catch (Exception e) {
            log.error("获取任务统计信息失败", e);
            throw new BusinessException("获取任务统计信息失败");
        }
    }

    /**
     * 获取需要督办的任务列表
     *
     * @return 督办任务列表
     */
    public List<TaskSupervisionDTO> getTasksForSupervision() {
        log.info("开始获取需要督办的任务列表");

        try {
            // 获取距离截止时间3小时内且未完成的任务
            LocalDateTime threeHoursLater = LocalDateTime.now().plusHours(3);

            List<TaskPO> urgentTasks = taskMapper.selectList(
                new LambdaQueryWrapper<TaskPO>()
                    .le(TaskPO::getDueDate, threeHoursLater)
                    .ge(TaskPO::getDueDate, LocalDateTime.now())
                    .ne(TaskPO::getStatus, TaskStatusEnum.COMPLETED.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED)
                    .orderByAsc(TaskPO::getDueDate));

            List<TaskSupervisionDTO> supervisionTasks = urgentTasks.stream()
                .map(this::buildTaskSupervisionDTO)
                .collect(Collectors.toList());

            log.info("获取到需要督办的任务数量：{}", supervisionTasks.size());

            return supervisionTasks;

        } catch (Exception e) {
            log.error("获取督办任务列表失败", e);
            throw new BusinessException("获取督办任务列表失败");
        }
    }

    /**
     * 根据任务ID获取督办信息
     *
     * @param taskId 任务ID
     * @return 督办任务信息
     */
    public TaskSupervisionDTO getTaskSupervisionById(Long taskId) {
        log.info("获取任务督办信息，taskId: {}", taskId);

        TaskPO taskPO = taskMapper.selectById(taskId);
        if (taskPO == null) {
            throw new BusinessException("任务不存在，taskId: " + taskId);
        }

        return buildTaskSupervisionDTO(taskPO);
    }

    /**
     * 构建状态统计详情
     */
    private List<TaskStatusStatisticsDTO.TaskStatusStatistics> buildStatusStatistics(Long totalTasks) {
        List<TaskStatusStatisticsDTO.TaskStatusStatistics> statusStatistics = new ArrayList<>();

        for (TaskStatusEnum status : TaskStatusEnum.values()) {
            Long count = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .eq(TaskPO::getStatus, status.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));
            Double percentage = totalTasks > 0 ? (count.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;

            statusStatistics.add(TaskStatusStatisticsDTO.TaskStatusStatistics.builder()
                .statusCode(status.getCode())
                .statusName(status.getDesc())
                .count(count)
                .percentage(Math.round(percentage * 100.0) / 100.0)
                .build());
        }

        return statusStatistics;
    }

    /**
     * 构建优先级统计详情
     */
    private List<TaskStatusStatisticsDTO.TaskPriorityStatistics> buildPriorityStatistics(Long totalTasks) {
        List<TaskStatusStatisticsDTO.TaskPriorityStatistics> priorityStatistics = new ArrayList<>();

        for (TaskPriorityEnum priority : TaskPriorityEnum.values()) {
            Long count = taskMapper.selectCount(
                new LambdaQueryWrapper<TaskPO>()
                    .eq(TaskPO::getPriority, priority.getCode())
                    .eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED));
            Double percentage = totalTasks > 0 ? (count.doubleValue() / totalTasks.doubleValue()) * 100 : 0.0;

            priorityStatistics.add(TaskStatusStatisticsDTO.TaskPriorityStatistics.builder()
                .priorityCode(priority.getCode())
                .priorityName(priority.getDesc())
                .count(count)
                .percentage(Math.round(percentage * 100.0) / 100.0)
                .build());
        }

        return priorityStatistics;
    }

    /**
     * 构建任务督办DTO
     */
    private TaskSupervisionDTO buildTaskSupervisionDTO(TaskPO taskPO) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dueDate = taskPO.getDueDate();

        // 计算剩余时间（小时）
        Long remainingHours = dueDate != null ?
            Duration.between(now, dueDate).toHours() : null;

        // 判断是否紧急（3小时内）
        Boolean isUrgent = remainingHours != null && remainingHours <= 3 && remainingHours >= 0;

        return TaskSupervisionDTO.builder()
            .taskId(taskPO.getId())
            .title(taskPO.getTitle())
            .description(taskPO.getDescription())
            .ownerOpenId(taskPO.getOwnerOpenId())
            .ownerName(taskPO.getOwnerName())
            .priority(taskPO.getPriority().getCode())
            .priorityName(TaskPriorityEnum.getByCode(taskPO.getPriority().getCode()) != null ?
                TaskPriorityEnum.getByCode(taskPO.getPriority().getCode()).getDesc() : "未知")
            .status(taskPO.getStatus().getCode())
            .statusName(TaskStatusEnum.getByCode(taskPO.getStatus().getCode()) != null ?
                TaskStatusEnum.getByCode(taskPO.getStatus().getCode()).getDesc() : "未知")
            .dueDate(taskPO.getDueDate())
            .remainingHours(remainingHours)
            .isUrgent(isUrgent)
            .meetingId(taskPO.getMeetingId())
            .createTime(taskPO.getCreateTime())
            .build();
    }
}

package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.utils.jackson.JsonUtils;
import cn.july.orch.meeting.common.AgentConstants;
import cn.july.orch.meeting.domain.request.AgentRequest;
import cn.july.orch.meeting.domain.response.AgentCompleteRespDTO;
import cn.july.orch.meeting.properties.MeetingSeverProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.Disposable;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR> Assistant
 * @description 智能体应用服务
 */
@Slf4j
@Service
public class AgentInvokeService {

    private final AgentInvokeUtils agentInvokeUtils;
    private final MeetingSeverProperties properties;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public AgentInvokeService(AgentInvokeUtils agentInvokeUtils, MeetingSeverProperties properties, ObjectMapper objectMapper) {
        this.agentInvokeUtils = agentInvokeUtils;
        this.properties = properties;
        this.webClient = WebClient.builder().codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 * 1024))  // 设置最大内存大小为16MB
                .build();
        this.objectMapper = objectMapper;
    }

    /**
     * 调用智能体获取回答（非流式）
     */
    public AgentCompleteRespDTO invokeStreamGetAnswer(String appId, String userMessage) {
        return invokeStreamGetAnswer(appId, userMessage, null, AgentConstants.SUMMARY_AUTHORIZATION);
    }

    /**
     * 调用智能体获取回答（非流式）
     */
    public AgentCompleteRespDTO invokeStreamGetAnswer(String appId, String userMessage, Map<String, Object> variables, String authorization) {
        AgentRequest request = buildTextAgentRequest(appId, userMessage, variables);
        try {
            log.info("invoke stream get answer request body: {}", JsonUtils.toJson(request));
            AgentCompleteRespDTO completeResponse = getCompleteResponse(
                    properties.getAgent().getInvokeDomain() + AgentConstants.INVOKE_APP_URL,
                    JsonUtils.toJson(request),
                    authorization
            );
            log.info("invoke stream get answer response: {}", completeResponse);
            return completeResponse;
        } catch (Exception e) {
            log.error("Error getting agent answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    /**
     * 调用智能体处理文件并获取回答（非流式）
     */
    public AgentCompleteRespDTO invokeFileStreamGetAnswer(String appId, String fileUrl, String fileName, String customPrompt) {
        AgentRequest request = buildFileAgentRequest(appId, fileUrl, fileName, customPrompt);
        try {
            log.info("invoke file stream get answer request body: {}", JsonUtils.toJson(request));
            AgentCompleteRespDTO completeResponse = getCompleteResponse(
                    properties.getAgent().getInvokeDomain() + AgentConstants.INVOKE_APP_URL,
                    JsonUtils.toJson(request),
                    AgentConstants.SUMMARY_AUTHORIZATION
            );
            log.info("invoke file stream get answer response: {}", completeResponse);
            return completeResponse;
        } catch (Exception e) {
            log.error("Error getting agent file answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    /**
     * 调用智能体处理混合内容并获取回答（非流式）
     */
    public AgentCompleteRespDTO invokeMixedContentStreamGetAnswer(String appId, String textMessage,
                                                                  List<String> imageUrls, String fileUrl, String fileName) {
        AgentRequest request = buildMixedContentRequest(appId, textMessage, imageUrls, fileUrl, fileName);
        try {
            log.info("invoke mixed content stream get answer request body: {}", JsonUtils.toJson(request));
            AgentCompleteRespDTO completeResponse = getCompleteResponse(
                    properties.getAgent().getInvokeDomain() + AgentConstants.INVOKE_APP_URL,
                    JsonUtils.toJson(request),
                    AgentConstants.SUMMARY_AUTHORIZATION
            );
            log.info("invoke mixed content stream get answer response: {}", completeResponse);
            return completeResponse;
        } catch (Exception e) {
            log.error("Error getting agent mixed content answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    /**
     * 调用智能体处理文件并获取回答（带RawData信息和Variables参数，非流式）
     */
    public AgentCompleteRespDTO invokeFileWithRawDataAndVariablesStreamGetAnswer(String appId, String fileUrl, String fileName,
                                                                                 String customPrompt, RawData rawData,
                                                                                 Map<String, Object> variables) {
        AgentRequest request = buildFileWithRawDataAndVariablesAgentRequest(appId, fileUrl, fileName, customPrompt, rawData, variables);
        try {
            log.info("invoke file with raw data and variables stream get answer request body: {}", JsonUtils.toJson(request));
            AgentCompleteRespDTO completeResponse = getCompleteResponse(
                    properties.getAgent().getInvokeDomain() + AgentConstants.INVOKE_APP_URL,
                    JsonUtils.toJson(request),
                    AgentConstants.SUMMARY_AUTHORIZATION
            );
            log.info("invoke file with raw data and variables stream get answer response: {}", completeResponse);
            return completeResponse;
        } catch (Exception e) {
            log.error("Error getting agent file with raw data and variables answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    /**
     * 调用智能体获取回答（流式）
     */
    public SseEmitter invokeStreamGetAnswerStream(String appId, String userMessage, Map<String, Object> variables, String authorization) {
        AgentRequest request = buildTextAgentRequest(appId, userMessage, variables);
        try {
            log.info("invoke stream get answer request body: {}", JsonUtils.toJson(request));
            return getStreamResponse(
                    properties.getAgent().getInvokeDomain() + AgentConstants.INVOKE_APP_URL,
                    JsonUtils.toJson(request),
                    authorization
            );
        } catch (Exception e) {
            log.error("Error getting agent answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    /**
     * 调用智能体处理文件并获取回答（带RawData信息和Variables参数，流式）
     */
    public SseEmitter invokeFileWithRawDataAndVariablesStreamGetAnswerStream(String appId, String fileUrl, String fileName,
                                                                             String customPrompt, RawData rawData,
                                                                             Map<String, Object> variables, String authorization) {
        AgentRequest request = buildFileWithRawDataAndVariablesAgentRequest(appId, fileUrl, fileName, customPrompt, rawData, variables);
        try {
            log.info("invoke file with raw data and variables stream get answer request body: {}", JsonUtils.toJson(request));
            return getStreamResponse(
                    properties.getAgent().getInvokeDomain() + AgentConstants.INVOKE_APP_URL,
                    JsonUtils.toJson(request),
                    authorization
            );
        } catch (Exception e) {
            log.error("Error getting agent file with raw data and variables answer for appId {}: {}", appId, e.getMessage());
            return null;
        }
    }

    /**
     * 获取完整响应
     */
    private AgentCompleteRespDTO getCompleteResponse(String apiUrl, String body, String authorization) {
        try {
            CompletableFuture<AgentCompleteRespDTO> resultFuture = new CompletableFuture<>();
            StringBuilder answerBuilder = new StringBuilder();
            AtomicReference<JsonNode> lastInteractiveDataNode = new AtomicReference<>(null);
            webClient.post()
                    .uri(apiUrl)
                    .headers(h -> h.addAll(agentInvokeUtils.fetchJsonHeader(authorization)))
                    .bodyValue(body)
                    .retrieve()
                    .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                    })
                    .doOnNext(sse -> {
                        log.info("SSE Received: event='{}', data='{}'", sse.event(), sse.data());
                        String eventType = sse.event();
                        String eventData = sse.data();

                        if ("[DONE]".equals(eventData)) {
                            log.info("SSE stream [DONE] marker received.");
                            return;
                        }

                        try {
                            if (eventType != null) {
                                switch (eventType) {
                                    case "answer":
                                    case "fastAnswer":
                                        processAnswer(eventType, eventData, answerBuilder);
                                        break;
                                    case "interactive":
                                        lastInteractiveDataNode.set(objectMapper.readTree(eventData));
                                        log.info("Stored interactive event data.");
                                        break;
                                    case "flowResponses":
                                        log.debug("Received flowResponses event, data: {}", eventData);
                                        break;
                                    default:
                                        log.warn("Unsupported or unhandled SSE event type: {} or data format. Data: {}", eventType, eventData);
                                        break;
                                }
                            }
                        } catch (JsonProcessingException e) {
                            log.error("Failed to parse SSE data as JsonNode. Event: {}, Data: {}, Error: {}", eventType, eventData, e.getMessage(), e);
                        }
                    })
                    .doOnComplete(() -> {
                        log.info("SSE stream completed.");
                        AgentCompleteRespDTO finalDto = new AgentCompleteRespDTO();
                        finalDto.setAnswer(answerBuilder.toString());
                        JsonNode interactiveData = lastInteractiveDataNode.get();
                        fillInteractive(interactiveData, finalDto);
                        resultFuture.complete(finalDto);
                    })
                    .doOnError(throwable -> {
                        log.error("获取流式响应失败, url: {}, body: {}", apiUrl, body, throwable);
                        resultFuture.completeExceptionally(throwable);
                    })
                    .subscribe();

            return resultFuture.get();
        } catch (Exception e) {
            log.error("获取流式响应失败, url: {}, body: {}", apiUrl, body, e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 获取流式响应
     */
    private SseEmitter getStreamResponse(String apiUrl, String body, String authorization) {
        SseEmitter sseEmitter = new SseEmitter(5 * 60 * 1000L);

        // 获取上游的 Flux
        Disposable subscription = webClient.post()
                .uri(apiUrl)
                .headers(h -> h.addAll(agentInvokeUtils.fetchJsonHeader(authorization)))
                .bodyValue(body)
                .retrieve()
                .bodyToFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {})
                .subscribe(
                        // onNext
                        sse -> {
                            try {
                                // 你的业务逻辑 (JSON解析等) ...
                                String eventData = sse.data();
                                if (eventData == null) return;
                                // ... 省略部分解析代码 ...

                                String finalPayloadToSend = processSseData(sse); // 封装处理逻辑
                                if (finalPayloadToSend == null) return;

                                // 在这里发送数据
                                sseEmitter.send(SseEmitter.event()
                                        .name("answer")
                                        .data(finalPayloadToSend));

                                // 如果是结束信号，手动完成
                                if ("[DONE]".equals(eventData)) {
                                    sseEmitter.complete();
                                }

                            } catch (IOException | IllegalStateException e) {
                                // 关键修改：将 IOException 和 IllegalStateException 一同处理
                                // 它们都意味着与客户端的连接已不可用。
                                log.info("Client connection closed (or emitter already completed). Terminating stream. Message: {}", e.getMessage());
                                // 完成 emitter 会触发 onCompletion 回调
                                sseEmitter.complete();
                            } catch (Exception e) {
                                log.error("An unexpected error occurred while processing SSE event.", e);
                                sseEmitter.completeWithError(e);
                            }
                        },
                        // onError
                        error -> {
                            log.error("Error from upstream API.", error);
                            sseEmitter.completeWithError(error);
                        },
                        // onComplete
                        () -> {
                            log.info("Upstream stream completed.");
                            sseEmitter.complete();
                        }
                );

        // 当 SseEmitter 完成时（任何原因），确保取消上游订阅
        Runnable cleanup = () -> {
            if (!subscription.isDisposed()) {
                log.info("Cleaning up and disposing upstream subscription.");
                subscription.dispose();
            }
        };
        sseEmitter.onCompletion(cleanup);
        sseEmitter.onTimeout(cleanup);

        return sseEmitter;
    }

    // 将你的业务逻辑提取到一个辅助方法中，使主流程更清晰
    private String processSseData(ServerSentEvent<String> sse) throws JsonProcessingException {
        String eventData = sse.data();
        if (eventData == null) {
            return null;
        }

        if ("[DONE]".equals(eventData)) {
            return eventData;
        }

        String eventType = sse.event();
        if ("answer".equals(eventType) || "fastAnswer".equals(eventType)) {
            JsonNode answer = objectMapper.readTree(eventData);
            StringBuilder content = new StringBuilder();
            // ... (与之前完全相同的JSON解析逻辑) ...
            for (JsonNode choice : answer.path("choices")) {
                JsonNode delta = choice.path("delta");
                if (delta.has("reasoning_content") && delta.get("reasoning_content").asText(null) != null) {
                    continue;
                }
                String text = delta.path("content").asText(null);
                if (text != null) {
                    if ("fastAnswer".equals(eventType) && text.startsWith("\n")) {
                        text = text.substring(1);
                    }
                    content.append(text);
                }
            }
            if (content.length() > 0) {
                return JsonUtils.toJson(Collections.singletonMap("text", content.toString()));
            }
        }
        return null;
    }

    private void processAnswer(String eventType, String data, StringBuilder builder) {
        JsonNode answer;
        try {
            answer = objectMapper.readTree(data);
        } catch (JsonProcessingException e) {
            log.error("解析 JSON 失败, data: {}", data, e);
            return;
        }
        JsonNode choices = answer.path("choices");

        for (JsonNode choice : choices) {
            JsonNode delta = choice.path("delta");
            String content = delta.path("content").asText(null);

            if (content != null) {
                if ("fastAnswer".equals(eventType) && content.startsWith("\n")) {
                    content = content.substring(1);
                }
                builder.append(content);
            }
        }
    }

    private void fillInteractive(JsonNode interactiveData, AgentCompleteRespDTO finalDto) {
        if (interactiveData != null && interactiveData.has("interactive")) {
            JsonNode interactiveNode = interactiveData.path("interactive");
            AgentCompleteRespDTO.InteractiveContent dtoInteractive = new AgentCompleteRespDTO.InteractiveContent();
            dtoInteractive.setType(interactiveNode.path("type").asText(null));

            if (interactiveNode.has("params")) {
                JsonNode paramsNode = interactiveNode.path("params");
                AgentCompleteRespDTO.Params dtoParams = new AgentCompleteRespDTO.Params();
                dtoParams.setDescription(paramsNode.path("description").asText(null));

                // Populate inputForm if present
                if (paramsNode.has("inputForm") && paramsNode.path("inputForm").isArray()) {
                    List<AgentCompleteRespDTO.InputFormItem> inputFormItems = new ArrayList<>();
                    for (JsonNode itemNode : paramsNode.path("inputForm")) {
                        AgentCompleteRespDTO.InputFormItem item = new AgentCompleteRespDTO.InputFormItem();
                        item.setType(itemNode.path("type").asText(null));
                        item.setKey(itemNode.path("key").asText(null));
                        item.setLabel(itemNode.path("label").asText(null));
                        item.setDescription(itemNode.path("description").asText(null));
                        item.setValue(itemNode.path("value").asText(null));
                        item.setDefaultValue(itemNode.path("defaultValue").asText(null));
                        item.setValueType(itemNode.path("valueType").asText(null));
                        item.setRequired(itemNode.path("required").asBoolean(false));
                        Optional.ofNullable(itemNode.get("maxLength"))
                                .ifPresent(maxLengthNode -> item.setMaxLength(maxLengthNode.asInt()));
                        // 正确处理 list 字段为对象类型
                        if (itemNode.has("list") && itemNode.path("list").isArray()) {
                            List<AgentCompleteRespDTO.InputSelectOption> listValues = new ArrayList<>();
                            for (JsonNode listItem : itemNode.path("list")) {
                                AgentCompleteRespDTO.InputSelectOption option = new AgentCompleteRespDTO.InputSelectOption();
                                option.setLabel(listItem.path("label").asText(null));
                                option.setValue(listItem.path("value").asText(null));
                                listValues.add(option);
                            }
                            item.setList(listValues);
                        } else {
                            item.setList(null);
                        }
                        inputFormItems.add(item);
                    }
                    dtoParams.setInputForm(inputFormItems);
                }

                // Populate userSelectOptions if present
                if (paramsNode.has("userSelectOptions") && paramsNode.path("userSelectOptions").isArray()) {
                    List<AgentCompleteRespDTO.UserSelectOption> selectOptions = new ArrayList<>();
                    for (JsonNode optionNode : paramsNode.path("userSelectOptions")) {
                        AgentCompleteRespDTO.UserSelectOption option = new AgentCompleteRespDTO.UserSelectOption();
                        option.setKey(optionNode.path("key").asText(null));
                        option.setValue(optionNode.path("value").asText(null));
                        selectOptions.add(option);
                    }
                    dtoParams.setUserSelectOptions(selectOptions);
                }
                dtoInteractive.setParams(dtoParams);
            }
            finalDto.setInteractive(dtoInteractive);
        }
    }

    /**
     * RawData数据类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RawData {
        private Integer repoId;
        private Integer collectionId;
        private String filePlatform;
        private String externalFileId;
        private String externalFileUrl;
        private Long length;
        private String fileName;
        private String contentType;
        private Object metadata;
    }

    private AgentRequest buildTextAgentRequest(String appId, String
            userMessage, Map<String, Object> variables) {
        AgentRequest.Content textContent = AgentRequest.Content.builder()
                .type("text")
                .text(userMessage)
                .build();

        AgentRequest.MessageDTO messageDTO = AgentRequest.MessageDTO.builder()
                .content(java.util.Arrays.asList(textContent))
                .role("user")
                .hideInUI(false)
                .build();

        AgentRequest.AgentRequestBuilder builder = AgentRequest.builder()
                .appId(appId)
                .detail(true)
                .stream(true)
                .messages(java.util.Arrays.asList(messageDTO));

        if (variables != null && !variables.isEmpty()) {
            builder.variables(variables);
        }

        return builder.build();
    }

    private AgentRequest buildFileAgentRequest(String appId, String fileUrl, String fileName, String
            customPrompt) {
        List<AgentRequest.Content> contentList = new ArrayList<>();

        // 添加文件内容
        AgentRequest.Content fileContent = AgentRequest.Content.builder()
                .type("file_url")
                .name(fileName)
                .url(fileUrl)
                .build();
        contentList.add(fileContent);

        // 如果有自定义提示词，添加文本内容
        if (customPrompt != null && !customPrompt.trim().isEmpty()) {
            AgentRequest.Content textContent = AgentRequest.Content.builder()
                    .type("text")
                    .text(customPrompt)
                    .build();
            contentList.add(textContent);
        }

        AgentRequest.MessageDTO messageDTO = AgentRequest.MessageDTO.builder()
                .content(contentList)
                .role("user")
                .hideInUI(false)
                .build();

        return AgentRequest.builder()
                .appId(appId)
                .detail(true)
                .stream(true)
                .messages(java.util.Arrays.asList(messageDTO))
                .build();
    }

    private AgentRequest buildMixedContentRequest(String appId, String textMessage,
                                                  List<String> imageUrls, String fileUrl, String fileName) {
        List<AgentRequest.Content> contentList = new ArrayList<>();

        // 添加文本内容
        if (textMessage != null && !textMessage.trim().isEmpty()) {
            AgentRequest.Content textContent = AgentRequest.Content.builder()
                    .type("text")
                    .text(textMessage)
                    .build();
            contentList.add(textContent);
        }

        // 添加图片内容
        if (imageUrls != null && !imageUrls.isEmpty()) {
            for (String imageUrl : imageUrls) {
                AgentRequest.Content imageContent = AgentRequest.Content.builder()
                        .type("image_url")
                        .imageUrl(AgentRequest.ImageUrl.builder().url(imageUrl).build())
                        .build();
                contentList.add(imageContent);
            }
        }

        // 添加文件内容
        if (fileUrl != null && !fileUrl.trim().isEmpty()) {
            AgentRequest.Content fileContent = AgentRequest.Content.builder()
                    .type("file_url")
                    .name(fileName != null ? fileName : "document")
                    .url(fileUrl)
                    .build();
            contentList.add(fileContent);
        }

        AgentRequest.MessageDTO messageDTO = AgentRequest.MessageDTO.builder()
                .content(contentList)
                .role("user")
                .hideInUI(false)
                .build();

        return AgentRequest.builder()
                .appId(appId)
                .detail(true)
                .stream(true)
                .messages(java.util.Arrays.asList(messageDTO))
                .build();
    }

    private AgentRequest buildFileWithRawDataAndVariablesAgentRequest(String appId, String
                                                                              fileUrl, String fileName,
                                                                      String customPrompt, RawData rawData,
                                                                      Map<String, Object> variables) {
        List<AgentRequest.Content> contentList = new ArrayList<>();

        // 构建RawData对象
        AgentRequest.RawData agentRawData = AgentRequest.RawData.builder()
                .repoId(rawData.getRepoId())
                .collectionId(rawData.getCollectionId())
                .filePlatform(rawData.getFilePlatform())
                .externalFileId(rawData.getExternalFileId())
                .externalFileUrl(rawData.getExternalFileUrl())
                .length(rawData.getLength())
                .fileName(rawData.getFileName())
                .contentType(rawData.getContentType())
                .metadata(rawData.getMetadata())
                .build();

        // 添加文件内容（带RawData）
        AgentRequest.Content fileContent = AgentRequest.Content.builder()
                .type("file_url")
                .name(fileName)
                .url(fileUrl)
                .rawData(agentRawData)
                .build();

        contentList.add(fileContent);

        // 如果有自定义提示词，添加文本内容
        if (customPrompt != null && !customPrompt.trim().isEmpty()) {
            AgentRequest.Content textContent = AgentRequest.Content.builder()
                    .type("text")
                    .text(customPrompt)
                    .build();
            contentList.add(textContent);
        }

        AgentRequest.MessageDTO messageDTO = AgentRequest.MessageDTO.builder()
                .content(contentList)
                .role("user")
                .hideInUI(false)
                .build();

        // 构建基础请求
        AgentRequest.AgentRequestBuilder requestBuilder = AgentRequest.builder()
                .appId(appId)
                .detail(true)
                .stream(true)
                .messages(java.util.Arrays.asList(messageDTO));

        // 如果有variables参数，添加到请求中
        if (variables != null && !variables.isEmpty()) {
            requestBuilder.variables(variables);
        }

        return requestBuilder.build();
    }
}

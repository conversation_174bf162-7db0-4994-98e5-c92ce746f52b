package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.MeetingEvaluationStatisticsDTO;
import cn.july.orch.meeting.domain.po.MeetingEvaluationPO;
import cn.july.orch.meeting.mapper.MeetingEvaluationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 会议评价查询服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingEvaluationQueryService {

    private final MeetingEvaluationMapper meetingEvaluationMapper;

    /**
     * 根据会议ID获取评价统计信息
     */
    public MeetingEvaluationStatisticsDTO getEvaluationStatistics(Long meetingId) {
        if (meetingId == null) {
            return MeetingEvaluationStatisticsDTO.builder()
                    .totalCount(0)
                    .averageMeetingScore(0.0)
                    .averageContentScore(0.0)
                    .averageDurationScore(0.0)
                    .averageEffectivenessScore(0.0)
                    .scoreArray(Arrays.asList(0.0, 0.0, 0.0, 0.0))
                    .suggestionsList(new ArrayList<>())
                    .build();
        }

        try {
            // 查询评价条数
            Integer totalCount = meetingEvaluationMapper.countByMeetingId(meetingId);
            if (totalCount == null || totalCount == 0) {
                return MeetingEvaluationStatisticsDTO.builder()
                        .totalCount(0)
                        .averageMeetingScore(0.0)
                        .averageContentScore(0.0)
                        .averageDurationScore(0.0)
                        .averageEffectivenessScore(0.0)
                        .scoreArray(Arrays.asList(0.0, 0.0, 0.0, 0.0))
                        .suggestionsList(new ArrayList<>())
                        .build();
            }

            // 查询所有评价记录
            List<MeetingEvaluationPO> evaluations = meetingEvaluationMapper.selectByMeetingId(meetingId);
            
            // 计算各项评分的平均分
            Double averageMeetingScore = evaluations.stream()
                    .mapToInt(MeetingEvaluationPO::getMeetingScore)
                    .average()
                    .orElse(0.0);

            Double averageContentScore = evaluations.stream()
                    .mapToInt(MeetingEvaluationPO::getContentScore)
                    .average()
                    .orElse(0.0);

            Double averageDurationScore = evaluations.stream()
                    .mapToInt(MeetingEvaluationPO::getDurationScore)
                    .average()
                    .orElse(0.0);

            Double averageEffectivenessScore = evaluations.stream()
                    .mapToInt(MeetingEvaluationPO::getEffectivenessScore)
                    .average()
                    .orElse(0.0);

            // 构建评分数组
            List<Double> scoreArray = Arrays.asList(
                    averageMeetingScore,
                    averageContentScore,
                    averageDurationScore,
                    averageEffectivenessScore
            );

            // 查询改进建议列表
            List<String> suggestionsList = evaluations.stream()
                    .map(MeetingEvaluationPO::getSuggestions)
                    .filter(suggestion -> suggestion != null && !suggestion.trim().isEmpty())
                    .collect(Collectors.toList());

            return MeetingEvaluationStatisticsDTO.builder()
                    .totalCount(totalCount)
                    .averageMeetingScore(averageMeetingScore)
                    .averageContentScore(averageContentScore)
                    .averageDurationScore(averageDurationScore)
                    .averageEffectivenessScore(averageEffectivenessScore)
                    .scoreArray(scoreArray)
                    .suggestionsList(suggestionsList)
                    .build();

        } catch (Exception e) {
            log.error("获取会议评价统计信息失败，会议ID：{}", meetingId, e);
            return MeetingEvaluationStatisticsDTO.builder()
                    .totalCount(0)
                    .averageMeetingScore(0.0)
                    .averageContentScore(0.0)
                    .averageDurationScore(0.0)
                    .averageEffectivenessScore(0.0)
                    .scoreArray(Arrays.asList(0.0, 0.0, 0.0, 0.0))
                    .suggestionsList(new ArrayList<>())
                    .build();
        }
    }
} 
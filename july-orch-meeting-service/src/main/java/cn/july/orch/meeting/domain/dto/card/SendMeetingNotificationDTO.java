package cn.july.orch.meeting.domain.dto.card;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendMeetingNotificationDTO {

    /**
     * 送达人员
     */
    private String openId;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 会议名称
     */
    private String meetingName;

    /**
     * 会议时间
     */
    private String meetingTime;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议时长
     */
    private String meetingDuration;

    /**
     * 参会人员
     */
    private List<String> attendees;

    /**
     * 会议描述
     */
    private String meetingDescription;

    /**
     * 优先级
     */
    private String priorityLevel;

    /**
     * 通知类型
     */
    private String noticeType;

    /**
     * 提前时间
     */
    private String noticeTime;

    /**
     * 会议详情链接
     */
    private String meetingUrl;
} 
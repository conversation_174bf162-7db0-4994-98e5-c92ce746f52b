package cn.july.orch.meeting.service;

import cn.hutool.core.util.ObjUtil;
import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.assembler.NewMeetingConverter;
import cn.july.orch.meeting.config.UserInfoDTO;
import cn.july.orch.meeting.domain.entity.NewMeeting;
import cn.july.orch.meeting.domain.po.MeetingMinutePO;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.mapper.MeetingMinuteMapper;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议领域服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewMeetingDomainService {

    private final NewMeetingMapper newMeetingMapper;
    private final NewMeetingConverter newMeetingConverter;
    private final SsoActionService ssoActionService;
    private final FeishuAppClient feishuAppClient;
    private final MeetingMinuteMapper meetingMinuteMapper;

    /**
     * 创建会议
     */
    public void createMeeting(NewMeeting meeting) {
        // 设置初始状态
        meeting.setStatus(NewMeetingStatusEnum.NOT_STARTED);
        
        // 保存会议
        newMeetingMapper.insert(newMeetingConverter.toPO(meeting));
    }

    /**
     * 更新会议
     */
    public void updateMeeting(NewMeeting meeting) {
        newMeetingMapper.updateById(newMeetingConverter.toPO(meeting));
    }

    /**
     * 删除会议
     */
    public void deleteMeeting(Long id) {
        newMeetingMapper.deleteById(id);
    }

    /**
     * 根据ID查找会议
     */
    public NewMeeting findById(Long id) {
        NewMeetingPO po = newMeetingMapper.selectById(id);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    /**
     * 根据飞书日程事件ID查找会议
     */
    public NewMeeting findByFsCalendarEventId(String fsCalendarEventId) {
        NewMeetingPO po = newMeetingMapper.findByFsCalendarEventId(fsCalendarEventId);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    /**
     * 根据飞书会议ID查找会议
     */
    public NewMeeting findByFsMeetingId(String fsMeetingId) {
        NewMeetingPO po = newMeetingMapper.findByFsMeetingId(fsMeetingId);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    /**
     * 更新会议状态
     */
    public void updateMeetingStatus(Long id, NewMeetingStatusEnum status) {
        newMeetingMapper.updateStatus(id, status);
    }

    /**
     * 更新飞书相关信息
     */
    public void updateFeishuInfo(Long id, String fsCalendarEventId, String fsMeetingId, String meetingUrl) {
        newMeetingMapper.updateFeishuInfo(id, fsCalendarEventId, fsMeetingId, meetingUrl);
    }

    /**
     * 更新会议编号和飞书会议ID
     */
    public void updateMeetingNoAndFsMeetingId(Long id, String meetingNo, String fsMeetingId) {
        newMeetingMapper.updateMeetingNoAndFsMeetingId(id, meetingNo, fsMeetingId);
    }

    /**
     * 更新会议编号、飞书会议ID和会议链接
     */
    public void updateMeetingInfo(Long id, String meetingNo, String fsMeetingId, String meetingUrl) {
        newMeetingMapper.updateMeetingInfo(id, meetingNo, fsMeetingId, meetingUrl);
    }

    /**
     * 更新妙计链接
     */
    public void updateMinuteUrl(Long id, String minuteUrl) {
        newMeetingMapper.updateMinuteUrl(id, minuteUrl);
    }

    /**
     * 获取会议文档
     */
    public void getMinuteText(Long meetingId, String hostUserId, String minuteUrl) {
        try {
            // 1. 获取用户Token
            UserInfoDTO userToken = ssoActionService.getUserToken(hostUserId);
            if (ObjUtil.isNull(userToken)) {
                log.error("未获取到主持人UserAccessToken");
                return;
            }
            
            // 2. 调用飞书API获取纪要文本流
            ByteArrayOutputStream os = feishuAppClient.getMeetService()
                .getMinuteText(userToken.getUserAccessToken(), minuteUrl);
            
            // 3. 转换为String文本
            String minuteText = convertToText(os);
            
            // 4. 保存到数据库（存在则更新，不存在则添加）
            saveOrUpdateMinute(meetingId, minuteText, hostUserId);
            
            log.info("会议纪要保存成功，会议ID：{}", meetingId);
            
        } catch (Exception e) {
            log.error("获取会议纪要失败，会议ID：{}，错误：{}", meetingId, e.getMessage(), e);
        }
    }

    /**
     * 将ByteArrayOutputStream转换为String文本
     */
    private String convertToText(ByteArrayOutputStream os) {
        try {
            // 转换为UTF-8字符串
            String text = os.toString("UTF-8");
            return text;
        } catch (Exception e) {
            log.error("文本转换失败", e);
            throw new RuntimeException("文本转换失败", e);
        }
    }

    /**
     * 保存或更新会议纪要
     */
    private void saveOrUpdateMinute(Long meetingId, String minuteText, String userId) {
        try {
            // 检查是否已存在
            MeetingMinutePO existingMinute = meetingMinuteMapper.selectByMeetingId(meetingId);
            
            if (existingMinute != null) {
                // 存在则更新
                existingMinute.setMinuteText(minuteText);
                existingMinute.setMinuteTextLength(minuteText.length());
                existingMinute.setCreateUserId(userId);
                existingMinute.setCreateUserName(getUserName(userId));
                
                meetingMinuteMapper.updateById(existingMinute);
                log.info("更新会议纪要，会议ID：{}", meetingId);
            } else {
                // 不存在则新增
                MeetingMinutePO newMinute = new MeetingMinutePO();
                newMinute.setMeetingId(meetingId);
                newMinute.setMinuteText(minuteText);
                newMinute.setMinuteTextLength(minuteText.length());
                newMinute.setCreateUserId(userId);
                newMinute.setCreateUserName(getUserName(userId));
                
                meetingMinuteMapper.insert(newMinute);
                log.info("新增会议纪要，会议ID：{}", meetingId);
            }
            
        } catch (Exception e) {
            log.error("保存会议纪要失败，会议ID：{}，错误：{}", meetingId, e.getMessage(), e);
            throw new RuntimeException("保存会议纪要失败", e);
        }
    }

    /**
     * 根据用户ID获取用户名
     */
    private String getUserName(String userId) {
        try {
            // 这里可以根据实际需求调用用户服务获取用户名
            // 暂时返回userId，后续可以优化
            return userId;
        } catch (Exception e) {
            log.warn("获取用户名失败，用户ID：{}，使用默认值", userId);
            return userId;
        }
    }

    /**
     * 分页查询会议
     */
    public List<NewMeeting> findPage(String meetingName, Integer status, Integer priorityLevel,
                                   Long meetingPlanId, Long meetingStandardId, String startTimeFrom, 
                                   String startTimeTo, String createUserId, Integer pageNum, Integer pageSize) {
        IPage<NewMeetingPO> page = newMeetingMapper.findPage(meetingName, status, priorityLevel, meetingPlanId,
                meetingStandardId, startTimeFrom, startTimeTo, createUserId,
                pageNum, pageSize);
        return newMeetingConverter.toEntityList(page.getRecords());
    }

    /**
     * 统计会议数量
     */
    public Long count(String meetingName, Integer status, Integer priorityLevel,
                     Long meetingPlanId, Long meetingStandardId, String startTimeFrom, 
                     String startTimeTo, String createUserId) {
        return newMeetingMapper.count(meetingName, status, priorityLevel, meetingPlanId,
                                        meetingStandardId, startTimeFrom, startTimeTo, createUserId);
    }
} 
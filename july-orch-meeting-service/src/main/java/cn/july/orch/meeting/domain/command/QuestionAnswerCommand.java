package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> Assistant
 * @description 问答助手命令
 */
@Data
@ApiModel("问答助手命令")
public class QuestionAnswerCommand {

    @NotBlank(message = "问题不能为空")
    @ApiModelProperty("问题内容")
    private String question;
}
package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingEvaluationPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议评价Mapper
 * @date 2025-01-24
 */
@Mapper
public interface MeetingEvaluationMapper extends BaseMapper<MeetingEvaluationPO> {
    
    /**
     * 根据会议ID查询评价列表
     */
    List<MeetingEvaluationPO> selectByMeetingId(@Param("meetingId") Long meetingId);
    
    /**
     * 根据会议ID和评价人OpenID查询评价
     */
    MeetingEvaluationPO selectByMeetingIdAndEvaluator(@Param("meetingId") Long meetingId, 
                                                      @Param("evaluatorOpenId") String evaluatorOpenId);
} 
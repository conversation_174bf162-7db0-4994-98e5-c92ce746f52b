package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingEvaluationPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 会议评价Mapper
 */
@Repository
public interface MeetingEvaluationMapper extends BaseMapper<MeetingEvaluationPO> {

    /**
     * 根据会议ID查询评价统计信息
     */
    List<MeetingEvaluationPO> selectByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据会议ID查询评价条数
     */
    Integer countByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据会议ID查询各项评分的平均分
     */
    List<Double> selectAverageScoresByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据会议ID查询改进建议列表
     */
    List<String> selectSuggestionsByMeetingId(@Param("meetingId") Long meetingId);

    MeetingEvaluationPO selectByMeetingIdAndEvaluator(@Param("meetingId") Long meetingId, @Param("evaluatorOpenId") String evaluatorOpenId);
}
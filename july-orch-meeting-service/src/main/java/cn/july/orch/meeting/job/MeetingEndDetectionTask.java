package cn.july.orch.meeting.job;

import cn.hutool.core.util.ObjUtil;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.model.GetMeetingModel;
import cn.july.orch.meeting.common.Constants;
import cn.july.orch.meeting.domain.po.NewMeetingPO;
import cn.july.orch.meeting.enums.IsRecurringEnum;
import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.mapper.NewMeetingMapper;
import cn.july.orch.meeting.service.NewMeetingActionService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lark.oapi.service.vc.v1.model.GetMeetingRespBody;
import com.lark.oapi.service.vc.v1.model.Meeting;
import com.lark.oapi.service.vc.v1.model.MeetingParticipant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议结束检测定时任务
 * @date 2025-01-24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MeetingEndDetectionTask {

    private final NewMeetingActionService newMeetingActionService;
    private final NewMeetingMapper newMeetingMapper;
    private final FeishuAppClient feishuAppClient;

    /**
     * 检查飞书会议状态，处理可能遗漏的会议结束状态
     */
    @Scheduled(fixedRate = 600000) // 每10分钟执行一次
    public void checkFeishuMeetingStatus() {
        log.info("开始检查飞书会议状态，处理遗漏的会议结束");
        
        try {
            // 查询进行中的会议（只查询有飞书会议ID的）
            LambdaQueryWrapper<NewMeetingPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(NewMeetingPO::getStatus, NewMeetingStatusEnum.IN_PROCESS.getCode())
                       .isNotNull(NewMeetingPO::getFsMeetingId)
                       .ne(NewMeetingPO::getFsMeetingId, ""); // 飞书会议ID不为空
            
            List<NewMeetingPO> inProcessMeetings = newMeetingMapper.selectList(queryWrapper);
            
            for (NewMeetingPO meetingPO : inProcessMeetings) {
                try {
                    // 调用飞书API检查会议状态
                    boolean isMeetingEnded = checkFeishuMeetingStatus(meetingPO.getFsMeetingId());
                    
                    if (isMeetingEnded) {
                        // 更新会议状态为已结束
                        newMeetingMapper.updateStatus(meetingPO.getId(), NewMeetingStatusEnum.ENDED);
                        
                        // 如果是重复会议且未取消，生成下次会议
                        if (meetingPO.getIsRecurring() != null && meetingPO.getIsRecurring() == IsRecurringEnum.YES
                            && (meetingPO.getRecurrenceCancelled() == null || meetingPO.getRecurrenceCancelled() == 0)) {
                            newMeetingActionService.generateNextMeetingAfterEnd(meetingPO.getId());
                        }
                        
                        log.info("检测到飞书会议已结束，更新会议状态，会议ID：{}", meetingPO.getId());
                    }
                } catch (Exception e) {
                    log.error("检查飞书会议状态失败，会议ID：{}", meetingPO.getId(), e);
                }
            }
            
            log.info("飞书会议状态检查完成，检查了{}个进行中的会议", inProcessMeetings.size());
        } catch (Exception e) {
            log.error("检查飞书会议状态时发生异常", e);
        }
    }

    /**
     * 调用飞书API检查会议状态
     */
    private boolean checkFeishuMeetingStatus(String fsMeetingId) {
        try {
            // 调用飞书视频会议API获取会议状态
            GetMeetingModel fsMeetingReq = GetMeetingModel.builder()
                    .meetingId(fsMeetingId)
                    .withParticipants(true)
                    .userIdType(Constants.OPEN_ID)
                    .build();
            GetMeetingRespBody response = feishuAppClient.getMeetService().getMeeting(fsMeetingReq);

            if (ObjUtil.isNotNull(response) && ObjUtil.isNotNull(response.getMeeting())) {
                Meeting meeting = response.getMeeting();
                // 飞书会议状态：1-进行中，2-已结束，3-已取消
                // 返回true表示会议已结束
                boolean isEnded = meeting.getStatus() == 2;

                if (isEnded) {
                    log.debug("飞书会议已结束，会议ID：{}，状态：{}", fsMeetingId, meeting.getStatus());
                    
                    // 会议结束时，获取实际会议信息并更新到数据库
                    try {
                        updateMeetingActualInfo(fsMeetingId, meeting);
                    } catch (Exception e) {
                        log.error("更新会议实际执行信息失败，飞书会议ID：{}", fsMeetingId, e);
                    }
                }

                return isEnded;
            }
            
            log.debug("飞书会议状态检查，会议ID：{}，响应：{}", fsMeetingId, response);
            return false; // 如果无法获取状态，返回false避免误判
        } catch (Exception e) {
            log.error("调用飞书API检查会议状态失败，会议ID：{}", fsMeetingId, e);
            return false; // 出错时返回false，避免误判
        }
    }

    /**
     * 更新会议实际执行信息
     */
    private void updateMeetingActualInfo(String fsMeetingId, Meeting fsMeeting) {
        try {
            // 根据飞书会议ID查找本地会议
            NewMeetingPO meetingPO = newMeetingMapper.findByFsMeetingId(fsMeetingId);
            if (meetingPO == null) {
                log.warn("未找到对应的本地会议，飞书会议ID：{}", fsMeetingId);
                return;
            }

            // 获取实际开始时间和结束时间
            LocalDateTime actualStartTime = null;
            LocalDateTime actualEndTime = null;
            
            if (fsMeeting.getStartTime() != null) {
                actualStartTime = LocalDateTime.parse(fsMeeting.getStartTime().replace("Z", ""));
            }
            if (fsMeeting.getEndTime() != null) {
                actualEndTime = LocalDateTime.parse(fsMeeting.getEndTime().replace("Z", ""));
            }

            // 获取实际参会人员open_id列表
            List<String> actualAttendees = null;
            if (fsMeeting.getParticipants() != null && fsMeeting.getParticipants().length>0) {
                actualAttendees = Arrays.stream(fsMeeting.getParticipants())
                        .map(MeetingParticipant::getId)
                        .collect(Collectors.toList());
            }

            // 更新会议实际执行信息
            meetingPO.setActualStartTime(actualStartTime);
            meetingPO.setActualEndTime(actualEndTime);
            meetingPO.setActualAttendees(actualAttendees);

            // 更新到数据库
            newMeetingMapper.updateById(meetingPO);
            
            log.info("会议实际执行信息更新成功，会议ID：{}，实际开始时间：{}，实际结束时间：{}，实际参会人数：{}", 
                    meetingPO.getId(), actualStartTime, actualEndTime, 
                    actualAttendees != null ? actualAttendees.size() : 0);
                    
        } catch (Exception e) {
            log.error("更新会议实际执行信息失败，飞书会议ID：{}，错误：{}", fsMeetingId, e.getMessage(), e);
            throw new RuntimeException("更新会议实际执行信息失败", e);
        }
    }
}

package cn.july.orch.meeting.exception;


import cn.july.core.exception.MessageCodeWrap;

/**
 * 业务错误码定义
 * 从201-899
 *
 */
public enum MessageCode implements MessageCodeWrap {

    RESOURCE_NOT_EXIST("201", "资源不存在"),
    UPLOAD_FILE_ERROR("202","上传文件失败"),
    FILE_NOT_EXIST("203","文件不存在"),
    DOWNLOAD_FILE_ERROR("203","导出文件失败"),
    MATE_DATE_ERROR("204","元数据不正确"),


    AUTH_FAIL("401", "鉴权失败"),
    TOKEN_GET_ERROR("402", "未获取到token"),
    LOGIN_ERROR("403", "登录态异常"),
    TOKEN_INVALID("404", "token已失效,未获取到用户信息,请重新进行登录"),
    USER_INFO_ERROR("405","未获取到用户信息"),
    OPEN_ID_GET_ERROR("406","未获取到openId"),

    BUSINESS_MEETING_CONFIG_ALREADY_EXISTS("501","业务会议配置已存在"),
    START_END_TIME_NOT_EXIST("502", "日程起止时间不能为空"),
    MEETING_NOT_EXIST("503", "日程起止时间不能为空"),
    SING_IN_CODE_ERROR("504", "签到码错误，请重新输入"),
    DO_NOT_SIGN_IN_AGAIN("505", "请勿重复签到"),
    NO_PERMISSION_TO_DELETE("506", "非日程组织者，不允许删除"),
    NO_PERMISSION_TO_UPDATE("507", "非日程组织者，不允许更新"),
    MEETING_ENT_EXIST("508", "会议已结束,禁止签到"),
    EXIST_PERSONNEL_DUPLICATION("509", "人员：{0} 重复！"),
    USER_CALENDAR_INFO_ERROR("510","未获取到用户日历信息"),
    NO_ALLOW_TO_DELETE("511","当前会议状态不允许删除"),
    NO_ALLOW_TO_UPDATE("512","当前会议状态不允许编辑"),
    CHECK_IN_TIME_ERROR("513","未到达签到时间，不允许签到"),
    ALREADY_CHECK_IN_ERROR("514","已经签到，不必重复签到"),
    CHECK_IN_TIME_ENDED("515","会议已结束，不允许签到"),
    CURRENT_IDENTITY_NOT_ALLOW_UPLOADING("516","当前身份不允许上传"),
    UPLOAD_NOT_ALLOWED_BEFORE_MEETING_STARTS("517","会议未开始不允许上传"),
    EXCEEDING_UPLOAD_TIME("518","超过上传时间，不允许上传"),

    TEAM_LEVEL_DELETE_STOP("601","存在班组数据,禁止删除层级"),
    TEAM_LEVEL_DELETE_STOP2("602","存在下级数据,禁止删除"),
    TEAM_INFO_DELETE_STOP("603","存在会议数据,禁止删除"),
    TELEPHONE_NO_EXIST("604","手机号不存在"),

    ;

    private final String code;
    private final String description;

    MessageCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}

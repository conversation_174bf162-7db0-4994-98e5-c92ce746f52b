package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.july.orch.meeting.common.AgentConstants;
import cn.july.orch.meeting.domain.command.FileUploadSummaryCommand;
import cn.july.orch.meeting.domain.dto.KbRepoFileDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description AI赋能服务
 */
@Slf4j
@Service
public class AiEmpowermentService {

    @Resource
    private AgentInvokeService agentInvokeService;

    @Resource
    private ExternalFileUploadService externalFileUploadService;

    /**
     * 默认的文件汇总智能体应用ID
     */
    private static final String DEFAULT_FILE_SUMMARY_APP_ID = "688c63597e9027870e39c5d9";

    /**
     * 文件上传汇总提炼（流式）
     */
    public SseEmitter fileUploadSummaryStream(FileUploadSummaryCommand command) {
        log.info("开始文件上传汇总提炼，文件名：{}", command.getFile().getOriginalFilename());

        try {
            // 调用外部接口上传文件
            KbRepoFileDTO fileInfo = externalFileUploadService.uploadFile(command.getFile());
            if (fileInfo == null) {
                log.error("文件上传失败");
                return null;
            }

            // 构建自定义提示词
            StringBuilder promptBuilder = new StringBuilder();
            String customPrompt = StringUtils.hasText(command.getCustomPrompt()) ?
                                 command.getCustomPrompt() : "请对这个文档进行详细的汇总，重点关注关键信息和要点。";
            promptBuilder.append(customPrompt);
            
            // 添加关注点
            if (CollUtil.isNotEmpty(command.getFocusPoints())) {
                promptBuilder.append(" 重点关注：").append(command.getFocusPoints()).append("。");
            }
            
            // 添加字数限制
            if (command.getWords() != null && command.getWords() > 0) {
                promptBuilder.append(" 请控制汇总内容在").append(command.getWords()).append("字左右。");
            }

            String finalPrompt = promptBuilder.toString();

            // 构建RawData
            AgentInvokeService.RawData rawData = buildRawDataFromKbRepoFileDTO(fileInfo, command.getFilePlatform());

            // 获取应用ID
            String appId = StringUtils.hasText(command.getAppId()) ? command.getAppId() : DEFAULT_FILE_SUMMARY_APP_ID;

            // 构建variables参数
            Map<String, Object> variables = new HashMap<>();
            if (CollUtil.isNotEmpty(command.getFocusPoints())) {
                variables.put("focus_points", command.getFocusPoints());
            }
            if (command.getWords() != null && command.getWords() > 0) {
                variables.put("words", command.getWords());
            }

            // 调用智能体进行文件汇总（带rawData信息和variables）
            return agentInvokeService.invokeFileWithRawDataAndVariablesStreamGetAnswerStream(
                appId,
                fileInfo.getExternalFileUrl(),
                fileInfo.getFileName(),
                finalPrompt,
                rawData,
                variables,
                AgentConstants.SUMMARY_AUTHORIZATION
            );

        } catch (Exception e) {
            log.error("文件上传汇总提炼失败，文件名：{}", command.getFile().getOriginalFilename(), e);
            return null;
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String fileUrl) {
        if (!StringUtils.hasText(fileUrl)) {
            return "unknown_file";
        }

        try {
            // 从URL中提取文件名
            String[] parts = fileUrl.split("/");
            String fileName = parts[parts.length - 1];

            // 移除查询参数
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }

            return StringUtils.hasText(fileName) ? fileName : "unknown_file";
        } catch (Exception e) {
            log.warn("从URL提取文件名失败：{}", fileUrl, e);
            return "unknown_file";
        }
    }

    /**
     * 从KbRepoFileDTO构建RawData
     */
    private AgentInvokeService.RawData buildRawDataFromKbRepoFileDTO(KbRepoFileDTO kbRepoFileDTO, String filePlatform) {
        if (kbRepoFileDTO == null) {
            log.error("kbRepoFileDTO为null，无法构建RawData");
            return null;
        }

        return AgentInvokeService.RawData.builder()
                .repoId(kbRepoFileDTO.getRepoId() != null ? kbRepoFileDTO.getRepoId().intValue() : 0)
                .collectionId(kbRepoFileDTO.getCollectionId() != null ? kbRepoFileDTO.getCollectionId().intValue() : 0)
                .filePlatform(StringUtils.hasText(filePlatform) ? filePlatform : kbRepoFileDTO.getFilePlatform())
                .externalFileId(kbRepoFileDTO.getExternalFileId())
                .externalFileUrl(kbRepoFileDTO.getExternalFileUrl())
                .length(kbRepoFileDTO.getLength() != null ? kbRepoFileDTO.getLength().longValue() : 0L)
                .fileName(kbRepoFileDTO.getFileName())
                .contentType(kbRepoFileDTO.getContentType())
                .metadata(kbRepoFileDTO.getMetadata())
                .build();
    }
}
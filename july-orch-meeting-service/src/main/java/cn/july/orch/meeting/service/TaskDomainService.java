package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.entity.TaskAgg;
import cn.july.orch.meeting.enums.TaskPriorityEnum;
import cn.july.orch.meeting.enums.TaskStatusEnum;
import cn.july.orch.meeting.repository.ITaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务领域服务
 */
@Slf4j
@Service
public class TaskDomainService {

    @Resource
    private ITaskRepository taskRepository;

    /**
     * 创建任务
     *
     * @param title 任务标题
     * @param description 任务描述
     * @param ownerOpenId 负责人OpenID
     * @param ownerName 负责人名称
     * @param priority 优先级
     * @param dueDate 截止时间
     * @param meetingId 会议ID
     * @return 任务ID
     */
    public Long create(String title, String description, String ownerOpenId,
                      String ownerName, TaskPriorityEnum priority,
                      LocalDateTime dueDate, Long meetingId) {
        TaskAgg taskAgg = TaskAgg.create(title, description, ownerOpenId,
                                        ownerName, priority, dueDate, meetingId);
        return taskRepository.insert(taskAgg);
    }

    /**
     * 创建任务（指定操作用户）
     *
     * @param title 任务标题
     * @param description 任务描述
     * @param ownerOpenId 负责人OpenID
     * @param ownerName 负责人名称
     * @param priority 优先级
     * @param dueDate 截止时间
     * @param meetingId 会议ID
     * @param operatorOpenId 操作人OpenID
     * @param operatorName 操作人名称
     * @return 任务ID
     */
    public Long create(String title, String description, String ownerOpenId,
                      String ownerName, TaskPriorityEnum priority,
                      LocalDateTime dueDate, Long meetingId,
                      String operatorOpenId, String operatorName) {
        TaskAgg taskAgg = TaskAgg.create(title, description, ownerOpenId,
                                        ownerName, priority, dueDate, meetingId,
                                        operatorOpenId, operatorName);
        return taskRepository.insert(taskAgg);
    }

    /**
     * 更新任务
     *
     * @param taskId 任务ID
     * @param title 任务标题
     * @param description 任务描述
     * @param ownerOpenId 负责人OpenID
     * @param ownerName 负责人名称
     * @param priority 优先级
     * @param dueDate 截止时间
     * @param meetingId 会议ID
     */
    public void update(Long taskId, String title, String description, String ownerOpenId,
                      String ownerName, TaskPriorityEnum priority,
                      LocalDateTime dueDate, Long meetingId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.update(title, description, ownerOpenId, ownerName, priority, dueDate, meetingId);
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 更新任务聚合
     *
     * @param taskAgg 任务聚合
     */
    public void update(TaskAgg taskAgg) {
        if (taskAgg != null) {
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     */
    public void complete(Long taskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.complete();
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 开始任务
     *
     * @param taskId 任务ID
     */
    public void start(Long taskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.start();
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     */
    public void delete(Long taskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.delete();
            taskRepository.delete(taskAgg);
        }
    }

    /**
     * 根据ID查询任务
     *
     * @param taskId 任务ID
     * @return 任务聚合
     */
    public TaskAgg findById(Long taskId) {
        return taskRepository.findById(taskId);
    }

    /**
     * 根据会议ID查询任务列表
     *
     * @param meetingId 会议ID
     * @return 任务聚合列表
     */
    public List<TaskAgg> findByMeetingId(Long meetingId) {
        return taskRepository.findByMeetingId(meetingId);
    }

    /**
     * 根据状态查询任务列表
     *
     * @param status 任务状态
     * @return 任务聚合列表
     */
    public List<TaskAgg> findByStatus(TaskStatusEnum status) {
        return taskRepository.findByStatus(status);
    }

    /**
     * 根据负责人查询任务列表
     *
     * @param ownerOpenId 负责人OpenID
     * @return 任务聚合列表
     */
    public List<TaskAgg> findByOwner(String ownerOpenId) {
        return taskRepository.findByOwner(ownerOpenId);
    }

    /**
     * 处理超期任务
     */
    public void processOverdueTasks() {
        List<TaskAgg> overdueTasks = taskRepository.findOverdueTasks();
        for (TaskAgg taskAgg : overdueTasks) {
            if (taskAgg.isOverdue()) {
                taskAgg.markOverdue();
                taskRepository.update(taskAgg);
            }
        }
    }

    /**
     * 更新飞书任务ID
     *
     * @param taskId 任务ID
     * @param feishuTaskId 飞书任务ID
     */
    public void updateFeishuTaskId(Long taskId, String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findById(taskId);
        if (taskAgg != null) {
            taskAgg.updateFeishuTaskId(feishuTaskId);
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID完成任务
     *
     * @param feishuTaskId 飞书任务ID
     */
    public void completeByFeishuTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.complete();
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID取消完成任务（重新开始）
     *
     * @param feishuTaskId 飞书任务ID
     */
    public void uncompleteByFeishuTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.start(); // 将状态改为进行中
            taskRepository.update(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID删除任务
     *
     * @param feishuTaskId 飞书任务ID
     */
    public void deleteByFeishuTaskId(String feishuTaskId) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.delete();
            taskRepository.delete(taskAgg);
        }
    }

    /**
     * 根据飞书任务ID查询任务
     *
     * @param feishuTaskId 飞书任务ID
     * @return 任务聚合
     */
    public TaskAgg findByFeishuTaskId(String feishuTaskId) {
        return taskRepository.findByFeishuTaskId(feishuTaskId);
    }

    /**
     * 根据飞书任务ID更新任务信息
     *
     * @param feishuTaskId 飞书任务ID
     * @param title 任务标题
     * @param description 任务描述
     * @param ownerOpenId 负责人OpenID
     * @param ownerName 负责人名称
     * @param priority 优先级
     * @param dueDate 截止时间
     */
    public void updateByFeishuTaskId(String feishuTaskId, String title, String description,
                                   String ownerOpenId, String ownerName,
                                   TaskPriorityEnum priority, LocalDateTime dueDate) {
        TaskAgg taskAgg = taskRepository.findByFeishuTaskId(feishuTaskId);
        if (taskAgg != null) {
            taskAgg.update(title, description, ownerOpenId, ownerName, priority, dueDate, taskAgg.getInfo().getMeetingId());
            taskRepository.update(taskAgg);
        }
    }
}

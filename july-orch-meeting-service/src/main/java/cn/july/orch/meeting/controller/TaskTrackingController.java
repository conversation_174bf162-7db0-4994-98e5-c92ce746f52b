package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.dto.TaskListDTO;
import cn.july.orch.meeting.domain.dto.TaskTrackingStatisticsDTO;
import cn.july.orch.meeting.domain.query.TaskListQuery;
import cn.july.orch.meeting.domain.query.TaskStatisticsQuery;
import cn.july.orch.meeting.service.TaskTrackingQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 任务追踪控制器
 * @date 2025-01-28
 */
@Api(tags = "任务追踪")
@RestController
@RequestMapping("/task-tracking")
@RequiredArgsConstructor
public class TaskTrackingController {

    private final TaskTrackingQueryService taskTrackingQueryService;

    @PostMapping("/list")
    @ApiOperation("获取任务列表")
    public PageResultDTO<TaskListDTO> getTaskList(@Validated @RequestBody TaskListQuery query) {
        return taskTrackingQueryService.getTaskList(query);
    }

    @PostMapping("/statistics")
    @ApiOperation("获取任务统计数据")
    public TaskTrackingStatisticsDTO getTaskStatistics(@Validated @RequestBody TaskStatisticsQuery query) {
        return taskTrackingQueryService.getTaskStatistics(query);
    }
}

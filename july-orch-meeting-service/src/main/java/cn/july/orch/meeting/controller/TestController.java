package cn.july.orch.meeting.controller;

import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.domain.dto.SendMeetingEvaluationSurveyDTO;
import cn.july.orch.meeting.service.CardSendActionService;
import cn.july.orch.meeting.service.NewMeetingActionService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Random;

@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private NewMeetingActionService newMeetingActionService;
    @Resource
    private CardSendActionService cardSendActionService;

    @PostMapping("/hello")
    public Object hello() {
        newMeetingActionService.handleFeishuRecordReadyCallback("7539382603205181459","https://meetings.feishu.cn/minutes/obcnb1az3u482d7w16k39lb3");
        return null;
    }

    /**
     * 测试发送会议评价调查卡片
     */
    @PostMapping("/test-send-meeting-evaluation-survey")
    public Object testSendMeetingEvaluationSurvey() {
        try {
            // 生成随机测试数据
            Random random = new Random();
            
            SendMeetingEvaluationSurveyDTO surveyDTO = SendMeetingEvaluationSurveyDTO.builder()
                .meetingId(1L) // 随机会议ID 1-10000
                .meetingName("测试会议-" + (random.nextInt(1000) + 1))
                .meetingType("项目评审会议")
                .meetingTime("2025-01-20 14:00-16:00")
                .openId("ou_755ea7451f97828ca8038c179d0217e2") // 随机openId
                .evaluatorName("测试用户-" + (random.nextInt(100) + 1))
                .build();

            // 调用发送方法
            Object result = cardSendActionService.sendMeetingEvaluationSurvey(surveyDTO);
            
            return "会议评价调查卡片发送成功！\n" +
                   "测试数据：" + surveyDTO.toString() + "\n" +
                   "返回结果：" + result;
                   
        } catch (Exception e) {
            return "发送失败：" + e.getMessage() + "\n" + e.getStackTrace()[0];
        }
    }
}

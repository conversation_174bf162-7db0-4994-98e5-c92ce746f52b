package cn.july.orch.meeting.controller;

import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.domain.dto.SendMeetingEvaluationSurveyDTO;
import cn.july.orch.meeting.job.MeetingActualInfoSupplementTask;
import cn.july.orch.meeting.job.MinuteTextGenerationTask;
import cn.july.orch.meeting.service.CardSendActionService;
import cn.july.orch.meeting.service.NewMeetingActionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Random;

/**
 * <AUTHOR>
 * @description 测试控制器
 * @date 2025-01-24
 */
@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController {

    @Resource
    private FeishuAppClient feishuAppClient;
    
    private final NewMeetingActionService newMeetingActionService;
    private final MinuteTextGenerationTask minuteTextGenerationTask;
    private final MeetingActualInfoSupplementTask meetingActualInfoSupplementTask;
    private final CardSendActionService cardSendActionService;

    @GetMapping("/feishu-record-ready")
    public String testFeishuRecordReady() {
        newMeetingActionService.handleFeishuRecordReadyCallback("7539382603205181459","https://meetings.feishu.cn/minutes/obcnb1az3u482d7w16k39lb3");
        return null;
    }

    @GetMapping("/generate-minute-text")
    public String testGenerateMinuteText() {
        minuteTextGenerationTask.manualGenerateMinuteText();
        return "妙计视频转文字定时任务已手动触发";
    }

    @GetMapping("/supplement-meeting-actual-info")
    public String testSupplementMeetingActualInfo() {
        meetingActualInfoSupplementTask.manualSupplementMeetingActualInfo();
        return "会议实际信息补充定时任务已手动触发";
    }

    @PostMapping("/hello")
    public Object hello() {
        newMeetingActionService.handleFeishuRecordReadyCallback("7539382603205181459","https://meetings.feishu.cn/minutes/obcnb1az3u482d7w16k39lb3");
        return null;
    }

    /**
     * 测试发送会议评价调查卡片
     */
    @PostMapping("/test-send-meeting-evaluation-survey")
    public Object testSendMeetingEvaluationSurvey() {
        try {
            // 生成随机测试数据
            Random random = new Random();
            
            SendMeetingEvaluationSurveyDTO surveyDTO = SendMeetingEvaluationSurveyDTO.builder()
                .meetingId(3L) // 随机会议ID 1-10000
                .meetingName("测试会议-" + (random.nextInt(1000) + 1))
                .meetingType("项目评审会议")
                .meetingTime("2025-01-20 14:00-16:00")
                .openId("ou_755ea7451f97828ca8038c179d0217e2") // 随机openId
//                .openId("ou_ca11635b2ad48203b793d90d78f10a0e") // 随机openId
                .evaluatorName("测试用户-" + (random.nextInt(100) + 1))
                .build();

            // 调用发送方法
            Object result = cardSendActionService.sendMeetingEvaluationSurvey(surveyDTO);
            
            return "会议评价调查卡片发送成功！\n" +
                   "测试数据：" + surveyDTO.toString() + "\n" +
                   "返回结果：" + result;
                   
        } catch (Exception e) {
            return "发送失败：" + e.getMessage() + "\n" + e.getStackTrace()[0];
        }
    }
}

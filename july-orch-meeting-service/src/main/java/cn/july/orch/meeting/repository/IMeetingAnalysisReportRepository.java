package cn.july.orch.meeting.repository;

import cn.july.orch.meeting.domain.entity.MeetingAnalysisReport;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告仓储接口
 */
public interface IMeetingAnalysisReportRepository {

    /**
     * 保存会议分析报告
     */
    void save(MeetingAnalysisReport meetingAnalysisReport);

    /**
     * 更新会议分析报告
     */
    void update(MeetingAnalysisReport meetingAnalysisReport);

    /**
     * 根据ID查询会议分析报告
     */
    MeetingAnalysisReport findById(Long id);

    /**
     * 根据会议ID查询会议分析报告
     */
    MeetingAnalysisReport findByMeetingId(Long meetingId);

    /**
     * 删除会议分析报告
     */
    void deleteById(Long id);
}
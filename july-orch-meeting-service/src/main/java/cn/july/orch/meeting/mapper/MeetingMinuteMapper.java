package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingMinutePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 会议纪要数据访问层
 *
 * <AUTHOR>
 */
@Mapper
public interface MeetingMinuteMapper extends BaseMapper<MeetingMinutePO> {
    
    /**
     * 根据会议ID查询纪要
     */
    @Select("SELECT * FROM meeting_minute WHERE meeting_id = #{meetingId}")
    MeetingMinutePO selectByMeetingId(@Param("meetingId") Long meetingId);
}

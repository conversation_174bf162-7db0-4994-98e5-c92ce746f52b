package cn.july.orch.meeting.handler.task;

import cn.july.orch.meeting.enums.TaskNotificationTypeEnum;
import cn.july.orch.meeting.service.TaskSyncService;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 任务取消完成
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskCancelCompletedChangedHandler implements TaskNotificationHandler {

    private final TaskSyncService taskSyncService;

    @Override
    public void handle(P2TaskUpdatedV1Data notification) {
        log.info("处理任务取消完成事件，飞书任务ID：{}", notification.getTaskId());
        try {
            // 取消完成，将任务状态同步为未完成
            taskSyncService.syncTaskStatus(notification.getTaskId(), false);
            log.info("任务取消完成处理成功，飞书任务ID：{}", notification.getTaskId());
        } catch (Exception e) {
            log.error("处理任务取消完成事件失败，飞书任务ID：{}", notification.getTaskId(), e);
        }
    }

    @Override
    public TaskNotificationTypeEnum getObjType() {
        return TaskNotificationTypeEnum.TASK_UNCOMPLETED;
    }
}

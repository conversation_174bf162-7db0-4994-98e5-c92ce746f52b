package cn.july.orch.meeting.domain.dto;

import cn.july.orch.meeting.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.enums.ReportStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 会议分析报告DTO
 */
@Data
@ApiModel("会议分析报告DTO")
public class MeetingAnalysisReportDTO {

    @ApiModelProperty("报告ID")
    private Long id;

    @ApiModelProperty("关联的会议ID")
    private Long meetingId;

    @ApiModelProperty("会议名称")
    private String meetingName;

    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "报告状态")
    private ReportStatusEnum status;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "参会人数")
    private Integer attendeeCount;

    @ApiModelProperty("综合评分 (0-100)")
    private Integer overallScore;

    @ApiModelProperty("综合评分等级 (优秀/良好/一般/待改进)")
    private String overallScoreLevel;

    @ApiModelProperty("综合评分的描述文字")
    private String overallSummary;

    @ApiModelProperty("内容质量分析")
    private ContentAnalysis contentAnalysis;

    @ApiModelProperty("AI优化建议列表")
    private List<AiSuggestion> aiSuggestions;

    @ApiModelProperty("任务统计信息")
    private TaskStatisticsDTO taskStatistics;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}

package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.entity.MeetingPlan;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 会议规划转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingPlanConverter {

    MeetingPlanPO toPO(MeetingPlan meetingPlan);

    MeetingPlan toEntity(MeetingPlanPO meetingPlanPO);
}

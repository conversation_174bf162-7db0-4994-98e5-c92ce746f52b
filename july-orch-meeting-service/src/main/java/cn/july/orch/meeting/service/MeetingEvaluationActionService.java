package cn.july.orch.meeting.service;

import cn.july.core.exception.BusinessException;
import cn.july.orch.meeting.domain.agg.MeetingEvaluationAgg;
import cn.july.orch.meeting.domain.command.MeetingEvaluationSubmitCommand;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.dto.SendMeetingEvaluationSurveyDTO;
import cn.july.orch.meeting.domain.entity.MeetingEvaluationInfo;
import cn.july.orch.meeting.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议评价服务
 * @date 2025-01-24
 */
@Service
@Slf4j
public class MeetingEvaluationActionService {

    @Resource
    private MeetingEvaluationDomainService evaluationDomainService;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private NewMeetingQueryService newMeetingQueryService;

    /**
     * 新会议系统会议结束后发送评价卡片
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendNewMeetingEvaluationCards(Long newMeetingId) {
        log.info("开始发送新会议评价卡片，会议ID：{}", newMeetingId);

        // 获取新会议信息
        NewMeetingDTO newMeeting = newMeetingQueryService.getById(newMeetingId);
        if (newMeeting == null) {
            log.error("未找到新会议信息，会议ID：{}", newMeetingId);
            return;
        }

        // 获取参会人员列表
        List<String> attendeeOpenIds = newMeeting.getAttendees();
        if (CollectionUtils.isEmpty(attendeeOpenIds)) {
            log.warn("新会议没有参会人员，会议ID：{}", newMeetingId);
            return;
        }

        // 获取参会人员姓名列表
        List<String> attendeeNames;
        if (!CollectionUtils.isEmpty(newMeeting.getAttendeeDetails())) {
            attendeeNames = newMeeting.getAttendeeDetails().stream()
                .map(FSUserInfoDTO::getName)
                .collect(Collectors.toList());
        } else {
            // 如果详细信息为空，使用openId作为姓名
            attendeeNames = attendeeOpenIds;
        }

        // 发送评价卡片
        for (int i = 0; i < attendeeOpenIds.size(); i++) {
            try {
                SendMeetingEvaluationSurveyDTO surveyDTO = SendMeetingEvaluationSurveyDTO.builder()
                    .meetingId(newMeetingId)
                    .meetingName(newMeeting.getMeetingName())
                    .meetingType("新会议") // 新会议系统的会议类型
                    .meetingTime(DateUtils.getTimeRange(newMeeting.getStartTime(), newMeeting.getEndTime()))
                    .openId(attendeeOpenIds.get(i))
                    .evaluatorName(attendeeNames.get(i))
                    .build();

                cardSendActionService.sendMeetingEvaluationSurvey(surveyDTO);

                log.info("新会议评价卡片发送成功，会议ID：{}，接收人：{}", newMeetingId, attendeeNames.get(i));
            } catch (Exception e) {
                log.error("新会议评价卡片发送失败，会议ID：{}，接收人：{}", newMeetingId, attendeeNames.get(i), e);
            }
        }
    }

    /**
     * 处理评价提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitEvaluation(MeetingEvaluationSubmitCommand command) {
        log.info("提交会议评价，会议ID：{}，评价人：{}", command.getMeetingId(), command.getEvaluatorOpenId());

        // 检查是否已经评价过
        MeetingEvaluationAgg existingEvaluation = evaluationDomainService.findByMeetingIdAndEvaluator(
            command.getMeetingId(), command.getEvaluatorOpenId());

        if (existingEvaluation != null) {
            throw new BusinessException("您已经评价过此会议");
        }

        // 创建新的评价聚合根
        MeetingEvaluationAgg evaluationAgg = MeetingEvaluationAgg.builder()
            .info(MeetingEvaluationInfo.builder()
                .meetingId(command.getMeetingId())
                .build())
            .build();

        // 提交评价
        evaluationAgg.submitEvaluation(
            command.getEvaluatorOpenId(),
            command.getEvaluatorName(),
            command.getEvaluationDTO()
        );

        // 保存评价结果
        evaluationDomainService.saveEvaluation(evaluationAgg);

        log.info("会议评价提交成功，会议ID：{}，评价人：{}", command.getMeetingId(), command.getEvaluatorOpenId());
    }
}

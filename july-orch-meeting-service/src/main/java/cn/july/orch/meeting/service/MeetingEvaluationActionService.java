package cn.july.orch.meeting.service;

import cn.hutool.core.collection.CollUtil;
import cn.july.core.exception.BusinessException;
import cn.july.feishu.FeishuAppClient;
import cn.july.orch.meeting.assembler.UserInfoAssembler;
import cn.july.orch.meeting.domain.agg.MeetingEvaluationAgg;
import cn.july.orch.meeting.domain.command.MeetingEvaluationSubmitCommand;
import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import cn.july.orch.meeting.domain.dto.NewMeetingDTO;
import cn.july.orch.meeting.domain.dto.SendMeetingEvaluationSurveyDTO;
import cn.july.orch.meeting.domain.entity.MeetingEvaluationInfo;
import cn.july.orch.meeting.utils.DateUtils;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议评价服务
 * @date 2025-01-24
 */
@Service
@Slf4j
public class MeetingEvaluationActionService {

    @Resource
    private MeetingEvaluationDomainService evaluationDomainService;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private UserInfoAssembler userInfoAssembler;

    /**
     * 新会议系统会议结束后发送评价卡片
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendNewMeetingEvaluationCards(NewMeetingDTO newMeeting) {
        log.info("开始发送新会议评价卡片，会议ID：{}", newMeeting.getId());
        //获取实际参会人
        List<FSUserInfoDTO> attendeeDetails = new ArrayList<>();
        List<String> userIds = newMeeting.getActualAttendees();
        if (CollUtil.isNotEmpty(userIds)) {
            List<User> users = feishuAppClient.getContactService().userBatch(userIds);
            Map<String, User> userMap = users.stream()
                    .collect(Collectors.toMap(User::getOpenId, user -> user));

            // 填充参会人员详细信息
            if (!CollectionUtils.isEmpty(users)) {
                attendeeDetails = users.stream()
                        .map(userMap::get)
                        .filter(Objects::nonNull)
                        .map(userInfoAssembler::user2DTO)
                        .collect(Collectors.toList());
            }
        }
        //未获取到实际参会人,就用计划参会人代替
        if (CollUtil.isEmpty(attendeeDetails)) {
            attendeeDetails.addAll(newMeeting.getAttendeeDetails());
            attendeeDetails.add(newMeeting.getHostUserDetail());
            attendeeDetails.add(newMeeting.getRecorderUserDetail());
        }

//        List<FSUserInfoDTO> attendeeDetails = new ArrayList<>();
//        attendeeDetails.addAll(newMeeting.getAttendeeDetails());
//        attendeeDetails.add(newMeeting.getHostUserDetail());
//        attendeeDetails.add(newMeeting.getRecorderUserDetail());

        if(CollUtil.isEmpty(attendeeDetails)){
            log.warn("新会议没有参会人员，会议ID：{}", newMeeting.getId());
            return;
        }
        attendeeDetails = attendeeDetails.stream().distinct().collect(Collectors.toList());
        List<String> attendeeNames = attendeeDetails.stream()
                .map(FSUserInfoDTO::getName)
                .collect(Collectors.toList());
        List<String> openIds = attendeeDetails.stream()
                .map(FSUserInfoDTO::getOpenId)
                .collect(Collectors.toList());

        // 发送评价卡片
        for (int i = 0; i < openIds.size(); i++) {
            try {
                SendMeetingEvaluationSurveyDTO surveyDTO = SendMeetingEvaluationSurveyDTO.builder()
                        .meetingId(newMeeting.getId())
                        .meetingName(newMeeting.getMeetingName())
                        .meetingType("新会议") // 新会议系统的会议类型
                        .meetingTime(DateUtils.getTimeRange(newMeeting.getStartTime(), newMeeting.getEndTime()))
                        .openId(openIds.get(i))
                        .evaluatorName(attendeeNames.get(i))
                        .build();

                cardSendActionService.sendMeetingEvaluationSurvey(surveyDTO);

                log.info("新会议评价卡片发送成功，会议ID：{}，接收人：{}", newMeeting.getId(), attendeeNames.get(i));
            } catch (Exception e) {
                log.error("新会议评价卡片发送失败，会议ID：{}，接收人：{}", newMeeting.getId(), attendeeNames.get(i), e);
            }
        }
    }

    /**
     * 处理评价提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitEvaluation(MeetingEvaluationSubmitCommand command) {
        log.info("提交会议评价，会议ID：{}，评价人：{}", command.getMeetingId(), command.getEvaluatorOpenId());

        // 检查是否已经评价过
        MeetingEvaluationAgg existingEvaluation = evaluationDomainService.findByMeetingIdAndEvaluator(
                command.getMeetingId(), command.getEvaluatorOpenId());

        if (existingEvaluation != null) {
            throw new BusinessException("您已经评价过此会议");
        }

        // 创建新的评价聚合根
        MeetingEvaluationAgg evaluationAgg = MeetingEvaluationAgg.builder()
                .info(MeetingEvaluationInfo.builder()
                        .meetingId(command.getMeetingId())
                        .build())
                .build();

        // 提交评价
        evaluationAgg.submitEvaluation(
                command.getEvaluatorOpenId(),
                command.getEvaluatorName(),
                command.getEvaluationDTO()
        );

        // 保存评价结果
        evaluationDomainService.saveEvaluation(evaluationAgg);

        log.info("会议评价提交成功，会议ID：{}，评价人：{}", command.getMeetingId(), command.getEvaluatorOpenId());
    }
}

package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 会议内容分析
 */
@Data
@ApiModel("会议内容分析")
public class ContentAnalysis {

    @ApiModelProperty("总体评分")
    private Integer score;

    @ApiModelProperty("各维度评分")
    private List<SubScore> subScores;

    @ApiModelProperty("分析发现列表")
    private List<Finding> findings;

    @Data
    @ApiModel("分析发现")
    public static class Finding {
        @ApiModelProperty("类型（positive-正面发现，suggestion-建议）")
        private String type;

        @ApiModelProperty("具体内容")
        private String text;
    }

    @Data
    public static class SubScore {
        @ApiModelProperty("维度名称")
        private String name;

        @ApiModelProperty("评分")
        private Integer value;
    }
}
package cn.july.orch.meeting.domain.query;

import cn.july.orch.meeting.enums.TimeUnitEnum;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 仪表板KPI查询
 * @date 2025-01-24
 */
@Data
public class DashboardKpiQuery {

    /**
     * 时间单位
     */
    private TimeUnitEnum timeUnit;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 用户ID
     */
    private Long userId;
}

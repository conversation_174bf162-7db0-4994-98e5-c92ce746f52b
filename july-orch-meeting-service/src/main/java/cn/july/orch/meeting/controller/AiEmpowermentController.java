package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.command.FileUploadSummaryCommand;
import cn.july.orch.meeting.service.AiEmpowermentService;
import cn.july.web.spring.annotation.ResponseResultWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description AI赋能控制器
 */
@Api(tags = "AI赋能")
@Slf4j
@RestController
@RequestMapping("/ai")
public class AiEmpowermentController {

    @Resource
    private AiEmpowermentService aiEmpowermentService;

    @ResponseResultWrapper(ignore = true)
    @PostMapping(value = "/file/upload-summary", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation(value = "文件上传汇总", notes = "上传文件并通过AI智能体进行汇总提炼，自动获取文件contentType")
    public SseEmitter fileUploadSummary(@ModelAttribute FileUploadSummaryCommand command) {
        log.info("收到文件上传汇总请求，文件名：{}，大小：{} bytes",
                command.getFile().getOriginalFilename(), command.getFile().getSize());

        if (command.getWords() == null) {
            command.setWords(500);
        }

        return aiEmpowermentService.fileUploadSummaryStream(command);
    }
}
package cn.july.orch.meeting.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Assistant
 * @description 任务统计DTO
 */
@Data
@ApiModel("任务统计DTO")
public class TaskStatisticsDTO {

    @ApiModelProperty("任务总数")
    private Integer totalCount;

    @ApiModelProperty("任务完成率（百分比）")
    private Double completionRate;

    @ApiModelProperty("各状态任务数量")
    private Map<String, Integer> statusCounts;

    @ApiModelProperty("任务列表")
    private List<TaskInfoDTO> tasks;
}
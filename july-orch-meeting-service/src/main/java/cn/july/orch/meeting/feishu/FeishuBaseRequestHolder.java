package cn.july.orch.meeting.feishu;

import cn.hutool.core.util.ObjUtil;
import cn.july.feishu.properties.FeishuProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 飞书基础请求holder
 * @date 2025-06-03
 */
@Component
public class FeishuBaseRequestHolder {

    @Resource
    private FeishuProperties feishuProperties;

    private FeishuBaseRequest feishuBaseRequest;

    @PostConstruct
    public void init() {
        if (ObjUtil.isNotNull(feishuProperties)) {
            this.feishuBaseRequest = FeishuBaseRequest.builder()
                .appId(feishuProperties.getAppId())
                .appSecret(feishuProperties.getAppSecret())
                .build();
        }
    }

    public FeishuBaseRequest get() {
        return feishuBaseRequest;
    }
}
